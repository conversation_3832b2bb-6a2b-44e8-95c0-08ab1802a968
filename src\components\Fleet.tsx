import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Car, Users, Truck, Crown, Luggage, Server } from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';
import SpecialQuoteModal from '@/components/SpecialQuoteModal';
import { useAnalytics, usePixel } from '@/shared';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { useServiceFleets } from '@/features/services-fleets';
import type { ServiceFleets } from '@/features/services-fleets';
import { useImageCache, getBestImageUrl } from '@/utils/imageCache';
import { Skeleton } from '@/components/ui/skeleton';

const VehicleCard = ({ vehicle }: { vehicle: ServiceFleets }) => {
  const { isImageLoaded, hasImageError, preloadImage } = useImageCache();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const bestImageUrl = getBestImageUrl(vehicle.imgurl_grande, vehicle.imgurl);

  useEffect(() => {
    if (bestImageUrl) {
      if (isImageLoaded(bestImageUrl)) {
        setImageLoaded(true);
        setImageError(false);
      } else if (hasImageError(bestImageUrl)) {
        setImageError(true);
        setImageLoaded(false);
      } else {
        preloadImage(bestImageUrl).then((success: boolean) => {
          setImageLoaded(success);
          setImageError(!success);
        });
      }
    }
  }, [bestImageUrl, isImageLoaded, hasImageError, preloadImage]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    setImageError(false);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(false);
  }, []);

  const features = [
    {
      icon: <Users className="w-4 h-4 mr-2" />,
      text: `${vehicle.passengers} Passengers`
    },
    {
      icon: <Luggage className="w-4 h-4 mr-2" />,
      text: `${vehicle.luggage} Luggages`
    },
    {
      icon: <Server className="w-4 h-4 mr-2" />,
      text: vehicle.service
    }
  ];

  return (
    <CarouselItem className="md:basis-1/2 lg:basis-1/3">
      <div className="p-1">
        <Card className={`hover-scale bg-white shadow-lg border-0 overflow-hidden relative h-full flex flex-col`}>
          <div className="h-48 bg-gradient-to-br from-cabo-blue to-cabo-turquoise flex items-center justify-center relative">
            {!imageLoaded && !imageError && <Skeleton className="w-full h-full" />}
            {imageError && (
              <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                <Car className="w-20 h-20 text-gray-400" />
              </div>
            )}
            <img
              src={bestImageUrl}
              alt={vehicle.name}
              className={`w-full h-full object-cover transition-opacity duration-300 ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
              onLoad={handleImageLoad}
              onError={handleImageError}
              loading="lazy"
            />
          </div>
          
          <CardContent className="p-6 flex flex-col flex-grow">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-1">
                  {vehicle.name}
                </h3>
                <p className="text-gray-600">{vehicle.vehicles}</p>
              </div>
            </div>

            <ul className="space-y-2 mb-6">
              {features.map((feature, idx) => (
                <li key={idx} className="flex items-center text-gray-600">
                  {feature.icon}
                  {feature.text}
                </li>
              ))}
            </ul>

            <div className="mt-auto">
              <Button 
                className={`w-full font-semibold bg-gray-100 hover:bg-gray-200 text-gray-900`}
              >
                Learn More
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </CarouselItem>
  );
}

const Fleet = () => {
  const { trackEvent } = useAnalytics();
  const pixel = usePixel();
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);
  const { data: vehicles, loading, error } = useServiceFleets();

  useEffect(() => {
    pixel.viewContent({ content_name: 'Fleet Section' });
  }, [pixel]);

  const handleCallNow = () => {
    trackEvent({
      action: 'click',
      category: 'Fleet CTA',
      label: 'Call Now Button'
    });
    pixel.contact({ contact_method: 'Phone' });
  };

  return (
    <section id="fleet" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <Badge className="bg-cabo-turquoise text-white mb-4">Our Fleet</Badge>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Modern and Comfortable Vehicles
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We have a modern fleet of vehicles equipped with the best amenities 
            to make your trip an unforgettable experience.
          </p>
        </div>

        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          className="w-full max-w-6xl mx-auto"
        >
          <CarouselContent>
            {loading && Array.from({ length: 3 }).map((_, index) => (
              <CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3">
                <div className="p-1">
                  <Card className="h-full flex flex-col">
                    <Skeleton className="h-48 w-full" />
                    <CardContent className="p-6 flex flex-col flex-grow">
                      <Skeleton className="h-6 w-3/4 mb-2" />
                      <Skeleton className="h-4 w-1/2 mb-4" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-2/3 mb-6" />
                      <div className="mt-auto">
                        <Skeleton className="h-10 w-full" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </CarouselItem>
            ))}
            {error && <p className="text-red-500">{error}</p>}
            {vehicles && vehicles.map((vehicle) => (
              <VehicleCard key={vehicle.id_fleet} vehicle={vehicle} />
            ))}
          </CarouselContent>
          <CarouselPrevious className="absolute left-[-50px] top-1/2 -translate-y-1/2 fill-black" />
          <CarouselNext className="absolute right-[-50px] top-1/2 -translate-y-1/2 fill-black" />
        </Carousel>

        <div className="text-center mt-16">
          <div className="bg-gray-50 rounded-2xl p-8 md:p-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Need a Special Vehicle?
            </h3>
            <p className="text-xl text-gray-600 mb-6">
              Contact us for personalized services, VIP vehicles or transfers for special events
            </p>
            <Button 
              size="lg" 
              className="bg-gradient-to-r from-cabo-sand to-yellow-400 text-cabo-blue hover:from-yellow-400 hover:to-cabo-sand font-semibold px-8 py-3 text-lg"
              onClick={handleCallNow}
            >
              Call Now
            </Button>
          </div>
        </div>
      </div>

      <SpecialQuoteModal 
        open={isQuoteModalOpen} 
        onOpenChange={setIsQuoteModalOpen} 
      />
    </section>
  );
};

export default Fleet;