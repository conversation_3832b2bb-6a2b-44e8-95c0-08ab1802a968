import { Facebook, Instagram } from 'lucide-react';
import { useAnalytics, usePixel } from '@/shared';

const TopBar = () => {
  const { trackEvent } = useAnalytics();
  const pixel = usePixel();

  const handleSocialClick = (socialMedia: string) => {
    trackEvent({
      action: 'click',
      category: 'TopBar Social',
      label: socialMedia
    });
    pixel.trackCustom('SocialLinkClick', { social_media: socialMedia });
  };

  const handleContactClick = (contactMethod: string) => {
    trackEvent({
      action: 'click',
      category: 'TopBar Contact',
      label: contactMethod
    });
    pixel.contact({ contact_method: contactMethod });
  };

  return (
    <div className="bg-gray-900 text-white py-0 text-sm pt-16 md:pt-2">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center">
          {/* Social Media Icons - Hidden on mobile */}
          <div className="hidden md:flex items-center space-x-4">
            <a 
              href="#" 
              className="hover:text-cabo-turquoise transition-colors duration-200"
              aria-label="Facebook"
              onClick={() => handleSocialClick('Facebook')}
            >
              <Facebook className="w-4 h-4" />
            </a>
            <a 
              href="#" 
              className="hover:text-cabo-turquoise transition-colors duration-200"
              aria-label="Instagram"
              onClick={() => handleSocialClick('Instagram')}
            >
              <Instagram className="w-4 h-4" />
            </a>
            <a 
              href="#" 
              className="hover:text-cabo-turquoise transition-colors duration-200"
              aria-label="TripAdvisor"
              onClick={() => handleSocialClick('TripAdvisor')}
            >
              <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"/>
              </svg>
            </a>
          </div>          

          {/* Contact Information - Desktop only */}
          <div className="hidden lg:flex items-center space-x-6">
            <a 
              href="mailto:<EMAIL>" 
              className="hover:text-cabo-turquoise transition-colors duration-200 flex items-center"
              onClick={() => handleContactClick('Email')}
            >
              <span className="mr-1">✉</span>
              <EMAIL>
            </a>
            <a 
              href="tel:+526241858009" 
              className="hover:text-cabo-turquoise transition-colors duration-200 flex items-center"
              onClick={() => handleContactClick('Phone')}
            >
              <span className="mr-1">📞</span>
              +52 **************
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopBar;