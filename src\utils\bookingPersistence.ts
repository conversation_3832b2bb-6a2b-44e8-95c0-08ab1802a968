import type { Retail } from '@/features/retails';

/**
 * Interface for the complete booking data from the frontend
 */
interface BookingFormData {
  contactInfo: {
    name?: string; // Legacy field for backward compatibility
    firstName?: string; // New field from form
    lastName?: string; // New field from form
    email: string;
    phone: string;
    whatsapp?: string;
  };
  flightInfo: {
    flightNumber?: string;
    airline?: string;
    arrivalDate?: string;
    arrivalTime?: string;
    departureDate?: string;
    departureTime?: string;
    departurePickup?: string;
  };
  additionalItems: {
    babySeat: string;
    carSeat: string;
    boosterSeat: string;
    specialInstructions?: string;
    extraServices: {
      stopShop?: boolean;
      golfBags?: boolean;
      surfboards?: boolean;
    };
  };
  selectedService?: any;
  serviceData?: {
    id: number;
    name: string;
    type: string;
    price: number;
  };
  payment: {
    method: string;
    transactionId: string;
    status: string;
    amount: number;
    currency: string;
    payerInfo?: any;
    captureId?: string;
    payerEmail?: string;
    payerName?: string;
  };
  totalPrice: number;
  createdAt: string;
}

/**
 * Interface for booking context state
 */
interface BookingState {
  from: string;
  to: string;
  date: Date | null;
  time: string;
  returnDate: Date | null;
  returnTime: string;
  adults: string;
  kids: string;
  roundTrip: boolean;
  selectedHotel?: any;
  fromAirportInfo?: any;
  selectedService?: any;
}

/**
 * Transform booking data to the format required by the retails API
 */
export function transformBookingToRetail(
  bookingData: BookingFormData,
  bookingState: BookingState,
  paypalDetails: any
): Retail {
  // Generate transaction ID
  const transactionId = `BT${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

  // Parse contact name - handle both legacy (name) and new (firstName/lastName) formats
  console.log('transformBookingToRetail - contactInfo:', bookingData.contactInfo);
  let firstName = '';
  let lastName = '';

  if (bookingData.contactInfo.firstName && bookingData.contactInfo.lastName) {
    // New format: firstName and lastName are separate fields
    firstName = bookingData.contactInfo.firstName.trim();
    lastName = bookingData.contactInfo.lastName.trim();
    console.log('Using firstName/lastName format:', { firstName, lastName });
  } else if (bookingData.contactInfo.name) {
    // Legacy format: name is a single field that needs to be split
    const nameParts = bookingData.contactInfo.name.trim().split(' ');
    firstName = nameParts[0] || '';
    lastName = nameParts.slice(1).join(' ') || '';
    console.log('Using legacy name format:', { name: bookingData.contactInfo.name, firstName, lastName });
  } else {
    // Fallback: try to get from individual fields even if they might be undefined
    firstName = (bookingData.contactInfo.firstName || '').trim();
    lastName = (bookingData.contactInfo.lastName || '').trim();
    console.log('Using fallback format:', { firstName, lastName });
  }
  
  // Calculate additional services costs
  const extraServices = bookingData.additionalItems.extraServices;
  const groceryStop = extraServices.stopShop ? 25.00 : 0.00;
  const golfClubsBags = extraServices.golfBags ? 30.00 : 0.00;
  const surfboards = extraServices.surfboards ? 20.00 : 0.00;
  
  // Format dates
  const formatDate = (dateStr?: string): string => {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return date.toISOString().split('T')[0]; // YYYY-MM-DD format
  };
  
  // Format time
  const formatTime = (timeStr?: string): string => {
    if (!timeStr) return '';
    // Ensure time is in HH:MM:SS format
    if (timeStr.length === 5) return `${timeStr}:00`;
    return timeStr;
  };
  
  // Build destination info
  const fromLocation = bookingState.fromAirportInfo?.name || bookingState.from || '';
  const toLocation = bookingState.selectedHotel?.name || bookingState.to || '';
  
  const retail: Retail = {
    transaction_id: transactionId,
    firstName: firstName,
    lastName: lastName,
    email: bookingData.contactInfo.email,
    phone_pax_full: bookingData.contactInfo.phone,
    subtotal: bookingData.totalPrice,
    payment_method: 'PayPal',
    fleet: {
      name: bookingData.serviceData?.name || 'Transportation Service'
    },
    roundtrip: bookingState.roundTrip,
    adults: parseInt(bookingState.adults) || 1,
    kids: parseInt(bookingState.kids) || 0,
    from: fromLocation,
    to: toLocation,
    
    // Arrival flight info
    arrivalAirline: bookingData.flightInfo.airline || '',
    arrivalFlight: bookingData.flightInfo.flightNumber || '',
    arrivalTime: formatTime(bookingData.flightInfo.arrivalTime),
    arrivalDate: new Date(formatDate(bookingData.flightInfo.arrivalDate) || new Date().toISOString()),
    
    // Departure flight info (for round trip)
    fromdep: bookingState.roundTrip ? toLocation : '',
    todep: bookingState.roundTrip ? fromLocation : '',
    departureAirline: bookingState.roundTrip ? (bookingData.flightInfo.airline || '') : '',
    departureFlight: bookingState.roundTrip ? (bookingData.flightInfo.flightNumber || '') : '',
    departureTime: bookingState.roundTrip ? formatTime(bookingData.flightInfo.departureTime) : '',
    departureDate: bookingState.roundTrip ? 
      new Date(formatDate(bookingData.flightInfo.departureDate) || new Date().toISOString()) : 
      new Date(),
    departurePickup: bookingData.flightInfo.departurePickup || 'Hotel Lobby',
    
    // Special instructions
    specialInstructions: bookingData.additionalItems.specialInstructions || '',
    
    // Child seats
    carSeats: parseInt(bookingData.additionalItems.carSeat) || 0,
    boosterSeats: parseInt(bookingData.additionalItems.boosterSeat) || 0,
    babySeats: parseInt(bookingData.additionalItems.babySeat) || 0,
    
    // Additional services
    add_grocery_stop: extraServices.stopShop || false,
    grocery_stop: groceryStop,
    add_golf_clubs_bags: extraServices.golfBags || false,
    golfClubsBags: extraServices.golfBags ? 1 : 0,
    golf_clubs_bags: golfClubsBags,
    add_surfboards: extraServices.surfboards || false,
    surfBoards: extraServices.surfboards ? 1 : 0,
    surfboards: surfboards,
    
    // PayPal authorization code
    authorization_code: paypalDetails.id || bookingData.payment.transactionId
  };
  
  return retail;
}

/**
 * Generate a unique transaction ID
 */
export function generateTransactionId(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `BT${timestamp}${random}`;
}

/**
 * Validate required fields for booking persistence
 */
export function validateBookingData(bookingData: BookingFormData, bookingState: BookingState): string[] {
  const errors: string[] = [];

  // Validate contact name - handle both legacy and new formats
  const hasName = bookingData.contactInfo.name?.trim();
  const hasFirstName = bookingData.contactInfo.firstName?.trim();
  const hasLastName = bookingData.contactInfo.lastName?.trim();

  if (!hasName && (!hasFirstName || !hasLastName)) {
    errors.push('Contact name is required (first name and last name)');
  }

  if (!bookingData.contactInfo.email?.trim()) {
    errors.push('Contact email is required');
  }

  if (!bookingData.contactInfo.phone?.trim()) {
    errors.push('Contact phone is required');
  }

  if (!bookingData.payment?.transactionId) {
    errors.push('Payment transaction ID is required');
  }

  if (!bookingData.totalPrice || bookingData.totalPrice <= 0) {
    errors.push('Total price must be greater than 0');
  }

  return errors;
}
