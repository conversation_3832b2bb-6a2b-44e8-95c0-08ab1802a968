// UI Hooks
export { useIsMobile, useViewport, useBreakpoints } from './ui/use-mobile';
export { useToast, toast, toastVariants, type ToasterToast } from './ui/use-toast';

// Analytics Hooks
export { default as useAnalytics } from './analytics/useAnalytics';
export type { AnalyticsEvent, PageViewEvent, EcommerceEvent } from './analytics/useAnalytics';

// Facebook Pixel
export { usePixel } from './facebook';

// Theme Hooks
export { useTheme, useThemeColors, useThemeGradients, useResponsiveTheme } from './theme/use-theme';
export type { ThemeVariant, UseThemeReturn } from './theme/use-theme';

// Re-export for backward compatibility
export { useTheme as default } from './theme/use-theme';
