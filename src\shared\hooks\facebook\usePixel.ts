import { useEffect, useCallback } from 'react';

const PIXEL_ID = import.meta.env.VITE_FACEBOOK_PIXEL_ID;

// Definición de eventos estándar de Facebook Pixel
type StandardEvent =
  | 'PageView'
  | 'ViewContent'
  | 'Search'
  | 'AddToCart'
  | 'InitiateCheckout'
  | 'AddPaymentInfo'
  | 'Purchase'
  | 'Lead'
  | 'CompleteRegistration'
  | 'Contact';

// Tipos para los datos de los eventos
interface EventData {
  [key: string]: any;
}

interface PurchaseData extends EventData {
  value: number;
  currency: string;
}

// Extender la interfaz Window para incluir fbq de forma segura
declare global {
  interface Window {
    fbq?: {
      (...args: any[]): void;
      queue?: any[];
      callMethod?: (...args: any[]) => void;
    };
  }
}

const usePixel = () => {
  useEffect(() => {
    if (!PIXEL_ID) {
      console.warn('Facebook Pixel ID not found in environment variables.');
      return;
    }

    if (window.fbq) {
      return;
    }

    // Carga el script de Facebook Pixel
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://connect.facebook.net/en_US/fbevents.js`;
    script.onload = () => {
      if (window.fbq) return; // Salir si ya está inicializado
      
      // Inicialización de la cola de fbq
      window.fbq = function(...args) {
        if (window.fbq?.callMethod) {
          window.fbq.callMethod.apply(window.fbq, args);
        } else if (window.fbq?.queue) {
          window.fbq.queue.push(args);
        }
      };
      window.fbq.queue = [];
      window.fbq('init', PIXEL_ID);
      window.fbq('track', 'PageView');
    };

    document.head.appendChild(script);

    return () => {
      // Opcional: limpiar el script si el componente se desmonta
      // document.head.removeChild(script);
    };
  }, []);

  // Hook useCallback para memorizar las funciones de tracking
  const track = useCallback((event: StandardEvent, data?: EventData) => {
    if (!window.fbq) {
      console.warn('Facebook Pixel not initialized.');
      return;
    }
    window.fbq('track', event, data);
  }, []);

  const trackCustom = useCallback((event: string, data?: EventData) => {
    if (!window.fbq) {
      console.warn('Facebook Pixel not initialized.');
      return;
    }
    window.fbq('trackCustom', event, data);
  }, []);

  // Funciones específicas para eventos estándar
  const pageView = useCallback(() => track('PageView'), [track]);
  const viewContent = useCallback((data: EventData) => track('ViewContent', data), [track]);
  const search = useCallback((data: EventData) => track('Search', data), [track]);
  const addToCart = useCallback((data: EventData) => track('AddToCart', data), [track]);
  const initiateCheckout = useCallback((data: EventData) => track('InitiateCheckout', data), [track]);
  const addPaymentInfo = useCallback((data: EventData) => track('AddPaymentInfo', data), [track]);
  const purchase = useCallback((data: PurchaseData) => track('Purchase', data), [track]);
  const lead = useCallback((data: EventData) => track('Lead', data), [track]);
  const completeRegistration = useCallback((data: EventData) => track('CompleteRegistration', data), [track]);
  const contact = useCallback((data: EventData) => track('Contact', data), [track]);

  return {
    track,
    trackCustom,
    pageView,
    viewContent,
    search,
    addToCart,
    initiateCheckout,
    addPaymentInfo,
    purchase,
    lead,
    completeRegistration,
    contact,
  };
};

export default usePixel;