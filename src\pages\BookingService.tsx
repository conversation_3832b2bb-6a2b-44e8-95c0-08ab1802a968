import { useEffect, useMemo, useCallback, useState } from 'react';
import { useLocation } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TopBar from '@/components/TopBar';
import SEO from '@/components/SEO';
import { BookingForm, type BookingFormDataWithHotel } from '@/components/booking-form';
import ServiceCard from '@/components/ServiceCard';
import { useServiceFleets } from '@/features/services-fleets/hooks/useServiceFleets';
import { useBooking } from '@/context/BookingContext';
import { useServiceFleetsRates } from '@/features/services-fleets/hooks/userServiceFleetRates';
import type { Hotel } from '@/features/hotels';
import { preloadServiceImages } from '@/utils/imageCache';
import {
  calculateTotalPassengers,
  calculateRequiredVehicles,
  generateServiceNote,
  calculateServicePrice,
  isAirportShuttleService
} from '@/utils/vehicleCalculations';

const BookingService = () => {
  const location = useLocation();
  const { state, updateFormData, setSelectedHotel, setRoundTrip, setSelectedService, getZoneId } = useBooking();
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Inicializar estado solo una vez al montar
  useEffect(() => {
    if (!location.state) return;

    const incomingData = location.state as Partial<BookingFormDataWithHotel>;
    
    // Si hay un hotel seleccionado, establecerlo primero
    if ('selectedHotel' in location.state && incomingData.selectedHotel) {
      setSelectedHotel(incomingData.selectedHotel);
    }

    updateFormData({
      from: incomingData.from || 'sjd',
      to: incomingData.to || '',
      date: incomingData.date ? new Date(incomingData.date) : undefined,
      time: incomingData.time || '',
      returnDate: incomingData.returnDate ? new Date(incomingData.returnDate) : undefined,
      returnTime: incomingData.returnTime || '',
      adults: incomingData.adults || '1',
      kids: incomingData.kids || '0',
      roundTrip: incomingData.roundTrip !== undefined ? incomingData.roundTrip : true
    });
  }, []); // Solo ejecutar al montar

  // Initial loading effect for better UX
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const { data: services, loading: servicesLoading, error: servicesError } = useServiceFleets();

  // Precargar imágenes cuando se cargan los servicios
  useEffect(() => {
    if (services && services.length > 0) {
      preloadServiceImages(services).then((successCount) => {
        // if (process.env.NODE_ENV === 'development') {
        //   console.log(`🖼️ ${successCount} service images preloaded for better UX`);
        // }
      });
    }
  }, [services]);

  // Obtener y validar zoneId usando helper del contexto
  const zoneId = useMemo(() => {
    const rawZoneId = getZoneId();
    if (!rawZoneId) {
      return '';
    }

    const id = Number(rawZoneId);
    const validId = !isNaN(id) && id > 0 ? id.toString() : '';

    return validId;
  }, [getZoneId]);

  // Obtener tarifas solo cuando tengamos un zoneId válido
  const { 
    serviceFleetsRates: rates, 
    loading: ratesLoading, 
    error: ratesError
  } = useServiceFleetsRates(zoneId);

  // Efecto para debug de tarifas (solo en desarrollo)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // console.log('Debug Rates:', {
      //   zoneId,
      //   roundTrip: state.roundTrip,
      //   ratesAvailable: !!rates && rates.length > 0,
      //   rateCount: rates?.length || 0,
      //   hotelSelected: !!state.selectedHotel,
      //   hotelZoneId: state.selectedHotel?.zone_id
      // });

      if (!zoneId) {
        console.warn('No zone_id for rates fetch:', { 
          zoneId,
          selectedHotel: state.selectedHotel 
        });
      }

      if (!rates && !ratesLoading && zoneId) {
        console.warn('No rates available for zone:', { 
          zoneId,
          loading: ratesLoading,
          error: ratesError 
        });
      }
    }
  }, [
    zoneId,
    state.roundTrip, 
    rates,
    state.selectedHotel,
    ratesLoading,
    ratesError
  ]);

  // Calcular total de pasajeros usando utilidad centralizada
  const getTotalPassengers = useCallback(() => {
    return calculateTotalPassengers(state.adults, state.kids);
  }, [state.adults, state.kids]);

  // Generar nota para servicios con información de vehículos
  const getServiceNote = useCallback((serviceName: string, serviceCapacity: string) => {
    const totalPassengers = getTotalPassengers();
    const calculation = calculateRequiredVehicles(totalPassengers, serviceCapacity);
    return generateServiceNote(calculation, state.roundTrip, serviceName);
  }, [state.roundTrip, getTotalPassengers]);

  // Encontrar la tarifa correspondiente basada en roundTrip y zoneId
  const findFleetRate = useCallback((fleetId: string, serviceName?: string, serviceCapacity?: string) => {
    // Verificar que tengamos un hotel seleccionado
    if (!state.selectedHotel) {
      return null;
    }

    // Verificar que tengamos un zoneId válido
    if (!zoneId) {
      return null;
    }

    if (ratesLoading) {
      return null;
    }

    if (!rates || rates.length === 0) {
      return null;
    }

    // Buscar la tarifa específica para este fleet y zona
    const fleetRate = rates.find(rate => {
      const rateFleetId = rate.fleet_id?.toString();
      // Convertir ambos valores a strings para la comparación
      const rateZoneId = rate.zone_id?.toString();

      return rateFleetId === fleetId && rateZoneId === zoneId;
    });

    if (!fleetRate) {
      return null;
    }

    try {
      // Obtener el precio según el tipo de viaje y validar
      const price = state.roundTrip ?
        Number(fleetRate.round_trip.price) :
        Number(fleetRate.one_way.price);

      if (isNaN(price) || price <= 0) {
        return null;
      }

      // Usar utilidades centralizadas para calcular el precio final
      const totalPassengers = getTotalPassengers();
      const isShuttle = isAirportShuttleService(fleetId, serviceName);

      if (isShuttle) {
        // Para Airport Shuttle, el precio es por persona
        return calculateServicePrice(price, {
          requiredVehicles: 1,
          totalPassengers,
          vehicleCapacity: parseInt(serviceCapacity || '1'),
          isMultipleVehicles: false
        }, true);
      }

      // Para otros servicios, calcular vehículos necesarios basado en capacidad
      if (serviceCapacity) {
        const calculation = calculateRequiredVehicles(totalPassengers, serviceCapacity);
        return calculateServicePrice(price, calculation, false);
      }

      return price;
    } catch (error) {
      console.error('Error processing rate:', error);
      return null;
    }
  }, [rates, state.roundTrip, zoneId, ratesLoading, state.selectedHotel, getTotalPassengers]);

  // Manejar cambios del formulario de manera optimizada
  const handleFormChange = useCallback((data: BookingFormDataWithHotel) => {
    console.log('Form change received:', data);

    // Función para comparar fechas
    const areDatesEqual = (date1?: Date | null, date2?: Date | null) => {
      if (!date1 && !date2) return true;
      if (!date1 || !date2) return false;
      return date1.getTime() === date2.getTime();
    };

    // Comparar cambios estructuralmente
    const hasRoundTripChanged = data.roundTrip !== state.roundTrip;
    const hasBasicDataChanged =
      data.from !== state.from ||
      data.to !== state.to ||
      !areDatesEqual(data.date, state.date) ||
      data.time !== state.time ||
      !areDatesEqual(data.returnDate, state.returnDate) ||
      data.returnTime !== state.returnTime ||
      data.adults !== state.adults ||
      data.kids !== state.kids;

    // Lógica inteligente para cambios de hotel
    // Solo actualizar el hotel si:
    // 1. Se seleccionó un hotel específico (data.selectedHotel existe)
    // 2. O si se limpió explícitamente el campo (data.to está vacío)
    const shouldUpdateHotel = data.selectedHotel !== undefined && (
      data.selectedHotel?.id !== state.selectedHotel?.id ||
      (data.selectedHotel === null && data.to === '')
    );

    // Batch updates para minimizar re-renders
    let updates: Partial<BookingFormDataWithHotel> = {};

    if (hasBasicDataChanged) {
      updates = {
        ...updates,
        from: data.from,
        to: data.to,
        date: data.date,
        time: data.time,
        returnDate: data.returnDate,
        returnTime: data.returnTime,
        adults: data.adults,
        kids: data.kids
      };
    }

    if (hasRoundTripChanged) {
      console.log('Round trip changed:', {
        new: data.roundTrip,
        old: state.roundTrip
      });
      updates.roundTrip = data.roundTrip;
    }

    // Actualizar el estado en una sola operación si hay cambios
    if (Object.keys(updates).length > 0) {
      updateFormData(updates);
    }

    // Manejar el cambio de hotel por separado solo cuando sea necesario
    if (shouldUpdateHotel) {
      console.log('Hotel changed:', {
        new: data.selectedHotel,
        old: state.selectedHotel,
        newZoneId: data.selectedHotel?.zone_id,
        reason: data.selectedHotel ? 'hotel_selected' : 'field_cleared'
      });
      setSelectedHotel(data.selectedHotel || null);
    } else if (data.selectedHotel === null && state.selectedHotel && data.to) {
      // Si el usuario está escribiendo pero hay un hotel seleccionado y texto en el campo,
      // mantener el hotel hasta que se seleccione uno nuevo o se limpie el campo
      console.log('Keeping existing hotel while user types:', {
        currentHotel: state.selectedHotel.name,
        currentText: data.to
      });
    }
  }, [state, setSelectedHotel, updateFormData]);

  // Efecto para sincronizar el estado cuando cambia el contexto
  useEffect(() => {
    if (state.selectedHotel && state.to) {
      console.log('Context state updated:', {
        hotel: state.selectedHotel,
        destination: state.to,
        date: state.date,
        roundTrip: state.roundTrip
      });
    }
  }, [state]);

  const handleBookNow = (serviceId: number) => {
    console.log('Booking service:', serviceId, 'with current state:', {
      selectedHotel: state.selectedHotel,
      to: state.to,
      date: state.date,
      roundTrip: state.roundTrip
    });

    // Encontrar el servicio seleccionado
    const selectedService = services?.find(service => Number(service.id_fleet) === serviceId);
    const fleetRate = selectedService ? findFleetRate(serviceId.toString(), selectedService.name, selectedService.passengers) : null;

    if (selectedService && fleetRate !== null) {
      // Guardar el servicio completo en el contexto
      const serviceData = {
        id: Number(selectedService.id_fleet),
        name: selectedService.name,
        image: selectedService.imgurl_grande || selectedService.imgurl || '',
        price: fleetRate,
        currency: 'USD',
        features: [
          ...(selectedService.description
            ? selectedService.description.split(/<br\s*\/?>/i).map((desc: string) => desc.trim()).filter(Boolean)
            : []),
          `Pax: ${selectedService.passengers}`,
          `Bags: ${selectedService.luggage}`
        ].filter(Boolean),
        capacity: Number(selectedService.passengers),
        type: selectedService.service,
        note: getServiceNote(selectedService.name, selectedService.passengers),
        isPopular: selectedService.name.includes('Luxury')
      };

      setSelectedService(serviceData);
      console.log('Service saved to context:', serviceData);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <SEO 
        title="Book Transportation Services - Los Cabos Airport Transportation"
        description="Choose from our premium transportation services in Los Cabos. SUV, Luxury, Van, Private Sprinter and Airport Shuttle options available."
        keywords="book transportation los cabos, reserve airport transfer, cabo transportation booking"
      />
      <TopBar />
      <Header />
      
      <div className="container mx-auto px-4 py-8 md:mt-16">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-4">
            Choose Your Perfect <span className="text-primary">Transportation</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Professional, reliable, and comfortable transportation services in Los Cabos. 
            Select the vehicle that best fits your needs and budget.
          </p>
        </div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Enhanced Booking Form */}
          <div className="lg:col-span-1">
            <BookingForm
              key={`${state.selectedHotel?.id || 'default'}`} // Solo re-render cuando cambia el hotel seleccionado
              variant="enhanced"
              onFormChange={handleFormChange} 
              initialData={useMemo(() => {
                const formData = {
                  from: state.from || 'sjd',
                  to: state.to || '',
                  date: state.date instanceof Date ? state.date :
                        typeof state.date === 'string' ? new Date(state.date) : null,
                  time: state.time || '',
                  returnDate: state.returnDate instanceof Date ? state.returnDate :
                             typeof state.returnDate === 'string' ? new Date(state.returnDate) : null,
                  returnTime: state.returnTime || '',
                  adults: state.adults || '1',
                  kids: state.kids || '0',
                  roundTrip: typeof state.roundTrip === 'boolean' ? state.roundTrip : true,
                  selectedHotel: state.selectedHotel || null
                };

                // console.log('BookingForm initialData:', formData);
                return formData;
              }, [state])} // Memoizar los datos iniciales
              autoNavigate={false}
              showContactInfo={true}
              showSpecialOffer={true}
              searchMode="search"
            />
          </div>

          {/* Enhanced Services List */}
          <div className="lg:col-span-3">
            <div className="space-y-8">
              {(servicesLoading || isInitialLoading || ratesLoading) && (
                <div className="space-y-6">
                  <div className="text-center mb-8">
                    <div className="inline-flex items-center gap-3 px-6 py-3 bg-white/5 backdrop-blur-sm rounded-full border border-white/10">
                      <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-lg text-primary font-medium">
                        {ratesLoading && !isInitialLoading ? 'Updating rates...' : 'Loading transportation options...'}
                      </span>
                    </div>
                  </div>
                  
                  {/* Animated Skeleton Cards */}
                  {[...Array(4)].map((_, index) => (
                    <div key={index} className="animate-fade-in" style={{animationDelay: `${index * 200}ms`}}>
                      <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-6 animate-pulse">
                        <div className="flex flex-col lg:flex-row gap-6">
                          {/* Image Skeleton */}
                          <div className="w-full lg:w-80 h-48 bg-gray-200 rounded-lg"></div>
                          
                          {/* Content Skeleton */}
                          <div className="flex-1 space-y-4">
                            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                            <div className="space-y-2">
                              <div className="h-4 bg-gray-200 rounded w-full"></div>
                              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
                            </div>
                            <div className="flex gap-2">
                              <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                              <div className="h-6 bg-gray-200 rounded-full w-20"></div>
                              <div className="h-6 bg-gray-200 rounded-full w-24"></div>
                            </div>
                          </div>
                          
                          {/* Price Skeleton */}
                          <div className="lg:w-48 space-y-4">
                            <div className="h-12 bg-gray-200 rounded w-full"></div>
                            <div className="h-10 bg-gray-200 rounded w-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {servicesError && !isInitialLoading && !ratesLoading && (
                <div className="text-center text-lg text-red-500 py-8">{servicesError}</div>
              )}
              
              {!isInitialLoading && !servicesLoading && !ratesLoading && services && services.length > 0 && (
                <div className="space-y-8 animate-fade-in">
                  {services
                    .map((service) => {
                      const fleetRate = findFleetRate(service.id_fleet, service.name, service.passengers);
                      return { service, fleetRate };
                    })
                    .filter(({ fleetRate }) => fleetRate !== null && fleetRate > 0) // Solo servicios con tarifas válidas
                    .map(({ service, fleetRate }, index) => (
                      <div 
                        key={service.id_fleet}
                        className="animate-fade-in hover-scale"
                        style={{animationDelay: `${index * 150}ms`}}
                      >
                        <ServiceCard
                          id={Number(service.id_fleet)}
                          name={service.name}
                          image={service.imgurl_grande || service.imgurl || ''}
                          price={fleetRate!} // Ya sabemos que no es null por el filter
                          currency="USD"
                          features={[
                            ...(service.description
                              ? service.description.split(/<br\s*\/?>/i).map((desc: string) => desc.trim()).filter(Boolean)
                              : []),
                            `Pax: ${service.passengers}`,
                            `Bags: ${service.luggage}`
                          ].filter(Boolean)}
                          capacity={service.passengers}
                          vehicles={service.vehicles}
                          type={service.service}
                          note={getServiceNote(service.name, service.passengers)}
                          isPopular={service.name.includes('Luxury')}
                          totalPassengers={getTotalPassengers()}
                          onBookNow={handleBookNow}
                        />
                      </div>
                    ))}
                </div>
              )}
              
              {!isInitialLoading && !ratesLoading && services && services.length === 0 && !servicesLoading && !servicesError && (
                <div className="text-center text-lg text-muted-foreground py-8">No services found.</div>
              )}
              {services && services.length > 0 &&
               services.every(service => {
                 const fleetRate = findFleetRate(service.id_fleet, service.name, service.passengers);
                 return fleetRate === null || fleetRate <= 0;
               }) && !servicesLoading && !servicesError && (
                <div className="text-center py-12">
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-6 max-w-md mx-auto">
                    <div className="flex items-center justify-center mb-4">
                      <svg className="w-12 h-12 text-amber-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-amber-800 mb-2">
                      No Transportation Available
                    </h3>
                    <p className="text-amber-700 text-sm">
                      Transportation services are not configured for this destination.
                      Please select a different hotel or contact support for assistance.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default BookingService;