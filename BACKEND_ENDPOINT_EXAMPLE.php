<?php
// Ejemplo de endpoint para tu CodeIgniter
// Archivo: application/controllers/api/Bookings.php

defined('BASEPATH') OR exit('No direct script access allowed');

class Bookings extends CI_Controller {
    
    public function __construct() {
        parent::__construct();
        $this->load->database();
        $this->load->helper('url');
        
        // Configurar headers para CORS
        header('Access-Control-Allow-Origin: http://localhost:8081');
        header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        header('Content-Type: application/json');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            exit(0);
        }
    }
    
    /**
     * Crear nueva reserva con pago de PayPal
     */
    public function create() {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON data']);
                return;
            }
            
            // Validar datos requeridos
            if (!isset($input['payment']['transactionId'])) {
                http_response_code(400);
                echo json_encode(['error' => 'PayPal transaction ID is required']);
                return;
            }
            
            // Preparar datos para la base de datos
            $booking_data = array(
                // Información de contacto
                'contact_name' => $input['contactInfo']['name'] ?? '',
                'contact_email' => $input['contactInfo']['email'] ?? '',
                'contact_phone' => $input['contactInfo']['phone'] ?? '',
                'contact_whatsapp' => $input['contactInfo']['whatsapp'] ?? '',
                
                // Información de vuelo
                'flight_number' => $input['flightInfo']['flightNumber'] ?? '',
                'airline' => $input['flightInfo']['airline'] ?? '',
                'arrival_date' => $input['flightInfo']['arrivalDate'] ?? null,
                'arrival_time' => $input['flightInfo']['arrivalTime'] ?? '',
                'departure_date' => $input['flightInfo']['departureDate'] ?? null,
                'departure_time' => $input['flightInfo']['departureTime'] ?? '',
                
                // Servicios adicionales
                'baby_seats' => $input['additionalItems']['babySeat'] ?? '0',
                'car_seats' => $input['additionalItems']['carSeat'] ?? '0',
                'booster_seats' => $input['additionalItems']['boosterSeat'] ?? '0',
                'special_instructions' => $input['additionalItems']['specialInstructions'] ?? '',
                'extra_services' => json_encode($input['additionalItems']['extraServices'] ?? []),
                
                // Información del servicio
                'service_id' => $input['serviceData']['id'] ?? null,
                'service_name' => $input['serviceData']['name'] ?? '',
                'service_type' => $input['serviceData']['type'] ?? '',
                'base_price' => $input['serviceData']['price'] ?? 0,
                
                // Información de pago PayPal
                'payment_method' => 'paypal',
                'paypal_transaction_id' => $input['payment']['transactionId'],
                'paypal_capture_id' => $input['payment']['captureId'] ?? null,
                'payment_status' => 'completed',
                'total_amount' => $input['totalPrice'] ?? 0,
                'currency' => $input['payment']['currency'] ?? 'USD',
                'payer_email' => $input['payment']['payerEmail'] ?? '',
                'payer_name' => $input['payment']['payerName'] ?? '',
                
                // Metadatos
                'booking_status' => 'confirmed',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                
                // Datos completos como JSON para referencia
                'raw_booking_data' => json_encode($input),
                'raw_payment_data' => json_encode($input['payment'])
            );
            
            // Insertar en base de datos
            $this->db->insert('paypal_bookings', $booking_data);
            $booking_id = $this->db->insert_id();
            
            if ($booking_id) {
                // Opcional: Enviar email de confirmación
                // $this->send_confirmation_email($booking_id, $booking_data);
                
                echo json_encode([
                    'success' => true,
                    'booking_id' => $booking_id,
                    'message' => 'Booking created successfully',
                    'paypal_transaction_id' => $input['payment']['transactionId']
                ]);
            } else {
                http_response_code(500);
                echo json_encode(['error' => 'Failed to create booking']);
            }
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Obtener reserva por ID
     */
    public function get($booking_id) {
        try {
            $query = $this->db->get_where('paypal_bookings', ['id' => $booking_id]);
            $booking = $query->row_array();
            
            if ($booking) {
                echo json_encode([
                    'success' => true,
                    'booking' => $booking
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Booking not found']);
            }
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Listar reservas (opcional, para admin)
     */
    public function list_bookings() {
        try {
            $this->db->order_by('created_at', 'DESC');
            $this->db->limit(50); // Limitar a 50 registros recientes
            $query = $this->db->get('paypal_bookings');
            $bookings = $query->result_array();
            
            echo json_encode([
                'success' => true,
                'bookings' => $bookings,
                'count' => count($bookings)
            ]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
        }
    }
}
