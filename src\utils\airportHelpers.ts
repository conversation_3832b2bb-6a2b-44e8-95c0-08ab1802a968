import type { AirportInfo } from '@/context/BookingContext';

/**
 * Información predefinida de aeropuertos comunes
 */
export const AIRPORT_INFO: Record<string, AirportInfo> = {
  'sjd': {
    id: 'sjd',
    name: 'Los Cabos International Airport',
    code: 'SJD',
    city: 'Los Cabos'
  },
  'lax': {
    id: 'lax',
    name: 'Los Angeles International Airport',
    code: 'LAX',
    city: 'Los Angeles'
  },
  'dfw': {
    id: 'dfw',
    name: 'Dallas/Fort Worth International Airport',
    code: 'DFW',
    city: 'Dallas'
  },
  'phx': {
    id: 'phx',
    name: 'Phoenix Sky Harbor International Airport',
    code: 'PHX',
    city: 'Phoenix'
  }
};

/**
 * Helper para obtener información del aeropuerto por ID
 */
export const getAirportInfo = (airportId: string): AirportInfo | null => {
  return AIRPORT_INFO[airportId.toLowerCase()] || null;
};

/**
 * Helper para crear información de aeropuerto desde datos de API
 */
export const createAirportInfo = (
  id: string,
  name: string,
  code: string,
  city?: string
): AirportInfo => {
  return {
    id,
    name,
    code,
    city: city || ''
  };
};

/**
 * Helper para formatear el nombre completo del aeropuerto
 */
export const formatAirportDisplay = (airportInfo: AirportInfo): string => {
  return `${airportInfo.name} (${airportInfo.code})`;
};
