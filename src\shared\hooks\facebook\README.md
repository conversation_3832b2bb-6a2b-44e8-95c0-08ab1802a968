# Facebook Pixel Hook

This hook provides a set of functions to track events with Facebook Pixel.

## Usage

To use the hook, import it in your component:

```typescript
import { usePixel } from '@/shared/hooks';

const MyComponent = () => {
  const pixel = usePixel();

  const handlePurchase = () => {
    pixel.purchase({ value: 100, currency: 'USD' });
  };

  return <button onClick={handlePurchase}>Buy Now</button>;
};
```

## Available Functions

- `track(event: string, data?: object)`: Tracks a standard event.
- `trackCustom(event: string, data?: object)`: Tracks a custom event.
- `pageView()`: Tracks a page view.
- `addToCart(data: object)`: Tracks an add to cart event.
- `initiateCheckout(data: object)`: Tracks an initiate checkout event.
- `purchase(data: object)`: Tracks a purchase event.
- `lead(data: object)`: Tracks a lead event.
- `viewContent(data: object)`: Tracks a view content event.
- `search(data: object)`: Tracks a search event.
- `contact(data: object)`: Tracks a contact event.
- `completeRegistration(data: object)`: Tracks a complete registration event.
