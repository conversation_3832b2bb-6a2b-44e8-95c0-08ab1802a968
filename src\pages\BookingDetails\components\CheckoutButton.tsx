import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import PayPalButton from '@/components/payments/PayPalButton';
import { useState } from 'react';
import { DollarSign, Wallet, CheckCircle, Shield, ShieldCheck, Lock } from 'lucide-react';

// Tipos para PayPal
interface PayPalOrderDetails {
  id: string;
  status: string;
  purchase_units: Array<{
    amount: {
      currency_code: string;
      value: string;
    };
    payments: {
      captures: Array<{
        id: string;
        status: string;
        amount: {
          currency_code: string;
          value: string;
        };
      }>;
    };
  }>;
  payer: {
    name: {
      given_name: string;
      surname: string;
    };
    email_address: string;
  };
}

interface PayPalError {
  message: string;
  details?: Array<{
    issue: string;
    description: string;
  }>;
}

// Tipos de métodos de pago disponibles
type PaymentMethod = 'cash' | 'paypal' | 'credit-card' | 'bank-transfer';

interface PaymentMethodInfo {
  id: PaymentMethod;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  enabled: boolean;
  badge?: string;
}

interface CheckoutButtonProps {
  onSubmit: (event: React.FormEvent) => void;
  isLoading?: boolean;
  disabled?: boolean;
  text?: string;
  // PayPal specific props
  amount?: string;
  currency?: string;
  description?: string;
  enablePayPal?: boolean;
  onPayPalSuccess?: (details: PayPalOrderDetails) => void;
  onPayPalError?: (error: PayPalError) => void;
  // Cash payment props
  onCashPayment?: () => void;
}

const CheckoutButton = ({
  onSubmit,
  isLoading = false,
  disabled = false,
  text = "Proceed to checkout",
  amount = "0.00",
  currency = "USD",
  description = "BajaTravel Transportation Service",
  enablePayPal = true,
  onPayPalSuccess,
  onPayPalError,
  onCashPayment
}: CheckoutButtonProps) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('paypal');

  // Configuración de métodos de pago disponibles (siempre visibles)
  const paymentMethods: PaymentMethodInfo[] = [
    {
      id: 'paypal',
      name: 'PayPal',
      description: 'Pay securely with PayPal',
      icon: Wallet,
      enabled: true, // Siempre habilitado para mostrar el tab
      badge: 'Secure'
    },
    {
      id: 'cash',
      name: 'Payment Cash',
      description: 'Pay in cash upon arrival',
      icon: DollarSign,
      enabled: true
    }
  ];

  const handlePayPalSuccess = (details: PayPalOrderDetails) => {
    console.log('PayPal payment successful:', details);
    if (onPayPalSuccess) {
      onPayPalSuccess(details);
    }
  };

  const handlePayPalError = (error: PayPalError) => {
    console.error('PayPal payment error:', error);
    if (onPayPalError) {
      onPayPalError(error);
    }
  };

  const handleCashPayment = () => {
    if (onCashPayment) {
      onCashPayment();
    } else {
      onSubmit({} as React.FormEvent);
    }
  };

  return (
    <div className="pt-0">
      <Card className="w-full">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-semibold">
            Choose Payment Method
          </CardTitle>
          <CardDescription>
            Select how you'd like to pay for your transportation service
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs
            value={selectedPaymentMethod}
            onValueChange={(value) => setSelectedPaymentMethod(value as PaymentMethod)}
            className="w-full"
          >
            {/* Payment Method Tabs */}
            <TabsList className="grid w-full grid-cols-2 mb-4">
              {paymentMethods.map((method) => {
                const IconComponent = method.icon;
                return (
                  <TabsTrigger
                    key={method.id}
                    value={method.id}
                    className="flex items-center space-x-2 py-3"
                    disabled={!method.enabled}
                  >
                    <IconComponent className="w-4 h-4" />
                    <span>{method.name}</span>
                    {method.badge && (
                      <Badge
                        variant="secondary"
                        className="ml-1 text-xs"
                      >
                        {method.badge}
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>

            

            {/* PayPal Payment Tab */}
            <TabsContent value="paypal" className="space-y-4">
              <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg">
                <CardContent className="pt-6">
                  {/* Header con iconos de PayPal y seguridad */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 bg-blue-600 p-2 rounded-lg">
                        <Wallet className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-bold text-blue-800 text-lg">PayPal Payment</h3>
                          <div className="flex items-center space-x-1">
                            <ShieldCheck className="w-5 h-5 text-green-600" />
                            <Lock className="w-4 h-4 text-green-600" />
                          </div>
                        </div>
                        <p className="text-sm text-blue-700 font-medium">
                          Pay securely with your PayPal account
                        </p>
                      </div>
                    </div>
                    {/* Logo de PayPal simulado */}
                    <div className="bg-white px-3 py-1 rounded-lg border border-blue-200 shadow-sm">
                      <span className="text-blue-600 font-bold text-lg">PayPal</span>
                    </div>
                  </div>

                  {/* Características de seguridad */}
                  <div className="bg-white rounded-lg p-4 mb-4 border border-blue-200 shadow-sm">
                    <div className="grid grid-cols-2 gap-4 mb-3">
                      <div className="flex items-center space-x-2">
                        <ShieldCheck className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">Secure Payment</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Lock className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">SSL Encrypted</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">Buyer Protection</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Shield className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium text-gray-700">Fraud Protection</span>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 pt-3">
                      <div className="flex justify-between items-center">
                        <span className="font-semibold text-gray-800">Total Amount:</span>
                        <span className="text-2xl font-bold text-blue-600">
                          ${amount} {currency}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Mensaje de confianza */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
                    <div className="flex items-center space-x-2">
                      <ShieldCheck className="w-5 h-5 text-green-600" />
                      <div>
                        <p className="text-sm font-semibold text-green-800">
                          100% Secure Transaction
                        </p>
                        <p className="text-xs text-green-700">
                          Your payment information is protected by PayPal's advanced security
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="w-full">
                    {parseFloat(amount) > 0 ? (
                      <PayPalButton
                        amount={amount}
                        currency={currency}
                        description={description}
                        onSuccess={handlePayPalSuccess}
                        onError={handlePayPalError}
                        disabled={disabled || isLoading}
                        style={{
                          layout: 'vertical',
                          color: 'gold',
                          shape: 'rect',
                          label: 'pay',
                          tagline: false,
                          height: 50
                        }}
                      />
                    ) : (
                      <Button
                        disabled={true}
                        className="w-full bg-gray-400 text-gray-600 py-3 text-lg font-semibold"
                        size="lg"
                      >
                        Enter booking details to enable PayPal
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Cash Payment Tab */}
            <TabsContent value="cash" className="space-y-4">
              <Card className="border-green-200 bg-green-50">
                <CardContent className="pt-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="flex-shrink-0">
                      <DollarSign className="w-8 h-8 text-green-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-green-800">Cash Payment</h3>
                      <p className="text-sm text-green-700">
                        Pay in cash when your driver arrives
                      </p>
                    </div>
                  </div>

                  <div className="bg-white rounded-lg p-4 mb-4 border border-green-200">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">Total Amount:</span>
                      <span className="text-xl font-bold text-green-600">
                        ${amount} {currency}
                      </span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                      No additional fees
                    </div>
                  </div>

                  {parseFloat(amount) > 0 ? (
                    <Button
                      onClick={handleCashPayment}
                      disabled={disabled || isLoading}
                      className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white py-3 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                      size="lg"
                    >
                      {isLoading ? "Processing..." : "Confirm Cash Payment"}
                    </Button>
                  ) : (
                    <Button
                      disabled={true}
                      className="w-full bg-gray-400 text-gray-600 py-3 text-lg font-semibold"
                      size="lg"
                    >
                      Enter booking details to enable Cash Payment
                    </Button>
                  )}
                </CardContent>
              </Card>
            </TabsContent>


          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default CheckoutButton;
