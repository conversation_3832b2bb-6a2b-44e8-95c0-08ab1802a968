sequenceDiagram
    participant U as Usuario
    participant F as Frontend
    participant P as PayPal
    participant API as Retails API

    U->>F: Completa formulario
    U->>F: Selecciona PayPal
    F->>P: Inicia pago
    P->>U: Procesa pago
    P->>F: Pago exitoso + detalles
    F->>F: Transforma datos
    F->>F: Valida datos
    F->>API: POST /save-trans-booking
    API->>F: Confirmación + transaction_id
    F->>U: Página de confirmación