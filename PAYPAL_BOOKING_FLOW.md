# PayPal Booking Flow Documentation

## Overview

Este documento describe el flujo completo de reservas con PayPal integrado con el sistema de persistencia de datos.

## Flujo de Datos

### 1. **Usuario completa el formulario de reserva**
- Información de contacto
- Información de vuelo
- Servicios adicionales
- Selección de vehículo/servicio

### 2. **Usuario selecciona PayPal como método de pago**
- Se muestra el botón de PayPal
- PayPal maneja el proceso de pago de forma segura

### 3. **PayPal procesa el pago exitosamente**
- PayPal devuelve detalles de la transacción
- Se ejecuta `handlePayPalSuccess` con los detalles

### 4. **Transformación de datos**
- Los datos del formulario se combinan con los detalles de PayPal
- Se validan los datos requeridos
- Se transforman al formato requerido por la API de retails

### 5. **Persistencia en base de datos**
- Se envían los datos al endpoint `/save-trans-booking`
- Se guarda la reserva con el transaction_id único
- Se confirma el guardado exitoso

### 6. **Navegación a página de confirmación**
- Se muestra la confirmación con todos los detalles
- Se incluye el transaction_id y PayPal ID

## Estructura de Datos

### Datos de Entrada (Frontend)
```typescript
interface BookingFormData {
  contactInfo: {
    name: string;
    email: string;
    phone: string;
    whatsapp?: string;
  };
  flightInfo: {
    flightNumber?: string;
    airline?: string;
    arrivalDate?: string;
    arrivalTime?: string;
    departureDate?: string;
    departureTime?: string;
    departurePickup?: string;
  };
  additionalItems: {
    babySeat: string;
    carSeat: string;
    boosterSeat: string;
    specialInstructions?: string;
    extraServices: {
      stopShop?: boolean;
      golfBags?: boolean;
      surfboards?: boolean;
    };
  };
  payment: {
    method: string;
    transactionId: string;
    status: string;
    amount: number;
    currency: string;
    // ... otros campos de PayPal
  };
}
```

### Datos de Salida (API Retails)
```typescript
interface Retail {
  transaction_id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone_pax_full: string;
  subtotal: number;
  payment_method: string;
  fleet: { name: string };
  roundtrip: boolean;
  adults: number;
  kids: number;
  from: string;
  to: string;
  arrivalAirline: string;
  arrivalFlight: string;
  arrivalTime: string;
  arrivalDate: Date;
  fromdep: string;
  todep: string;
  departureAirline: string;
  departureFlight: string;
  departureTime: string;
  departureDate: Date;
  departurePickup: string;
  specialInstructions: string;
  carSeats: number;
  boosterSeats: number;
  babySeats: number;
  add_grocery_stop: boolean;
  grocery_stop: number;
  add_golf_clubs_bags: boolean;
  golfClubsBags: number;
  golf_clubs_bags: number;
  add_surfboards: boolean;
  surfBoards: number;
  surfboards: number;
  authorization_code: string;
}
```

## Archivos Involucrados

### Frontend
- `src/pages/BookingDetails/index.tsx` - Página principal de reserva
- `src/components/payments/PayPalButton.tsx` - Componente de PayPal
- `src/pages/BookingConfirmation.tsx` - Página de confirmación
- `src/utils/bookingPersistence.ts` - Transformación de datos

### Features/Retails
- `src/features/retails/types/retails.types.ts` - Tipos TypeScript
- `src/features/retails/services/retails.service.ts` - Servicio API
- `src/features/retails/hooks/useRetails.ts` - Hooks React Query
- `src/features/retails/index.ts` - Exportaciones

### Configuración
- `src/config/config.ts` - Endpoints de API
- `.env` - Variables de entorno

## Funciones Clave

### `transformBookingToRetail()`
Transforma los datos del formulario al formato requerido por la API:
- Genera transaction_id único
- Parsea nombre completo en firstName/lastName
- Calcula costos de servicios adicionales
- Formatea fechas y horas
- Mapea campos del frontend a la estructura de la API

### `validateBookingData()`
Valida que los datos requeridos estén presentes:
- Nombre de contacto
- Email de contacto
- Teléfono de contacto
- Transaction ID de PayPal
- Precio total válido

### `useSaveBooking()`
Hook de React Query para guardar la reserva:
- Maneja el estado de loading/error
- Invalida cache después del guardado exitoso
- Proporciona feedback de errores

## Manejo de Errores

### Errores de Validación
- Se muestran al usuario antes de enviar a la API
- Incluyen campos faltantes o inválidos

### Errores de API
- Se capturan y muestran al usuario
- Se preserva el PayPal transaction ID para soporte
- Se registran en console para debugging

### Errores de PayPal
- Manejados por el componente PayPalButton
- Se muestran mensajes específicos al usuario

## Configuración de Producción

### Variables de Entorno
```bash
# PayPal
VITE_PAYPAL_CLIENT_ID=tu_client_id_de_produccion
VITE_PAYPAL_ENVIRONMENT=live

# API
VITE_API_BASE_URL_LOCAL=https://tu-api-produccion.com
```

### Endpoint de API
El endpoint debe estar configurado en:
```
POST /api/v1/save-trans-booking
```

## Testing

### Modo Sandbox
- Usar `VITE_PAYPAL_ENVIRONMENT=sandbox`
- Usar cuentas de prueba de PayPal
- Verificar que los datos se guarden correctamente

### Validación de Datos
- Probar con diferentes combinaciones de servicios
- Verificar cálculos de precios
- Confirmar formato de fechas y horas

## Monitoreo

### Logs Importantes
- PayPal transaction details
- Datos transformados antes de envío
- Respuestas de la API
- Errores de validación

### Métricas
- Tasa de conversión de pagos
- Errores de guardado
- Tiempo de respuesta de la API

## Soporte

### Información para Debugging
- PayPal Transaction ID
- Transaction ID interno (BT...)
- Timestamp de la transacción
- Datos completos del booking

### Contacto de Soporte
- Email: <EMAIL>
- Teléfono: +52 ************
