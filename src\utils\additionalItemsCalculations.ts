/**
 * Utilidades para cálculos de servicios adicionales y costos extra
 * Centraliza la lógica de cálculo de precios para servicios adicionales
 */

import type { AdditionalItemsFormData } from '@/pages/BookingDetails/schemas/bookingDetailsSchema';

// Precios de servicios extra (en USD)
export const EXTRA_SERVICE_PRICES = {
  stopShop: 30.00,
  golfBags: 20.00,    // por bolsa
  surfboards: 20.00   // por tabla
} as const;

// Child seats son gratuitos
export const CHILD_SEAT_PRICES = {
  babySeat: 0,
  carSeat: 0,
  boosterSeat: 0
} as const;

export interface ExtraServiceCost {
  service: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string;
}

export interface AdditionalItemsCostBreakdown {
  extraServices: ExtraServiceCost[];
  childSeats: ExtraServiceCost[];
  totalExtraServicesCost: number;
  totalChildSeatsCost: number;
  grandTotal: number;
}

/**
 * Calcula el costo de un servicio extra específico
 */
export const calculateExtraServiceCost = (
  serviceName: keyof typeof EXTRA_SERVICE_PRICES,
  isSelected: boolean,
  quantity: number = 1
): ExtraServiceCost => {
  const unitPrice = EXTRA_SERVICE_PRICES[serviceName];
  const actualQuantity = isSelected ? quantity : 0;
  const totalPrice = unitPrice * actualQuantity;

  const descriptions = {
    stopShop: 'Stop and Shop: Grocery Store, Supermarket (30 min)',
    golfBags: 'Golf clubs bags',
    surfboards: 'Surfboards'
  };

  return {
    service: serviceName,
    quantity: actualQuantity,
    unitPrice,
    totalPrice,
    description: descriptions[serviceName]
  };
};

/**
 * Calcula el costo de asientos para niños (siempre gratuitos)
 */
export const calculateChildSeatCost = (
  seatType: keyof typeof CHILD_SEAT_PRICES,
  quantity: string | number
): ExtraServiceCost => {
  const numQuantity = typeof quantity === 'string' ? parseInt(quantity) || 0 : quantity;
  const unitPrice = CHILD_SEAT_PRICES[seatType];
  const totalPrice = unitPrice * numQuantity;

  const descriptions = {
    babySeat: 'Baby Seat (0-1 years)',
    carSeat: 'Car Seat (2-4 years)',
    boosterSeat: 'Booster Seat (5-8 years)'
  };

  return {
    service: seatType,
    quantity: numQuantity,
    unitPrice,
    totalPrice,
    description: descriptions[seatType]
  };
};

/**
 * Calcula el desglose completo de costos adicionales
 */
export const calculateAdditionalItemsCosts = (
  additionalItems?: AdditionalItemsFormData
): AdditionalItemsCostBreakdown => {
  if (!additionalItems) {
    return {
      extraServices: [],
      childSeats: [],
      totalExtraServicesCost: 0,
      totalChildSeatsCost: 0,
      grandTotal: 0
    };
  }

  // Calcular servicios extra
  const extraServices: ExtraServiceCost[] = [
    calculateExtraServiceCost('stopShop', additionalItems.extraServices.stopShop),
    calculateExtraServiceCost('golfBags', additionalItems.extraServices.golfBags),
    calculateExtraServiceCost('surfboards', additionalItems.extraServices.surfboards)
  ].filter(service => service.quantity > 0);

  // Calcular asientos para niños
  const childSeats: ExtraServiceCost[] = [
    calculateChildSeatCost('babySeat', additionalItems.babySeat),
    calculateChildSeatCost('carSeat', additionalItems.carSeat),
    calculateChildSeatCost('boosterSeat', additionalItems.boosterSeat)
  ].filter(seat => seat.quantity > 0);

  // Calcular totales
  const totalExtraServicesCost = extraServices.reduce((sum, service) => sum + service.totalPrice, 0);
  const totalChildSeatsCost = childSeats.reduce((sum, seat) => sum + seat.totalPrice, 0);
  const grandTotal = totalExtraServicesCost + totalChildSeatsCost;

  return {
    extraServices,
    childSeats,
    totalExtraServicesCost,
    totalChildSeatsCost,
    grandTotal
  };
};

/**
 * Calcula el precio total del transporte incluyendo servicios adicionales
 */
export const calculateTotalTransportationPrice = (
  baseTransportPrice: number,
  additionalItems?: AdditionalItemsFormData
): {
  basePrice: number;
  additionalItemsCost: number;
  totalPrice: number;
  breakdown: AdditionalItemsCostBreakdown;
} => {
  const breakdown = calculateAdditionalItemsCosts(additionalItems);
  
  return {
    basePrice: baseTransportPrice,
    additionalItemsCost: breakdown.grandTotal,
    totalPrice: baseTransportPrice + breakdown.grandTotal,
    breakdown
  };
};

/**
 * Formatea un precio en USD
 */
export const formatPrice = (price: number): string => {
  return `$${price.toFixed(2)}`;
};

/**
 * Genera una descripción legible de los servicios adicionales seleccionados
 */
export const generateAdditionalItemsSummary = (
  additionalItems?: AdditionalItemsFormData
): string[] => {
  const breakdown = calculateAdditionalItemsCosts(additionalItems);
  const summary: string[] = [];

  breakdown.extraServices.forEach(service => {
    summary.push(`${service.description}: ${formatPrice(service.totalPrice)}`);
  });

  breakdown.childSeats.forEach(seat => {
    if (seat.quantity > 0) {
      summary.push(`${seat.description}: ${seat.quantity} seat${seat.quantity > 1 ? 's' : ''} (Free)`);
    }
  });

  return summary;
};
