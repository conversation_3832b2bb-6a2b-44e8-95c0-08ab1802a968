/**
 * Tests para las utilidades de cálculo de vehículos
 */

import {
  calculateTotalPassengers,
  calculateRequiredVehicles,
  generateServiceNote,
  calculateServicePrice,
  isAirportShuttleService,
  calculateServiceDetails
} from '../vehicleCalculations';

describe('vehicleCalculations', () => {
  describe('calculateTotalPassengers', () => {
    it('should calculate total passengers correctly with strings', () => {
      expect(calculateTotalPassengers('2', '1')).toBe(3);
      expect(calculateTotalPassengers('5', '0')).toBe(5);
      expect(calculateTotalPassengers('0', '3')).toBe(3);
    });

    it('should calculate total passengers correctly with numbers', () => {
      expect(calculateTotalPassengers(2, 1)).toBe(3);
      expect(calculateTotalPassengers(5, 0)).toBe(5);
      expect(calculateTotalPassengers(0, 3)).toBe(3);
    });

    it('should handle invalid inputs', () => {
      expect(calculateTotalPassengers('', '')).toBe(0);
      expect(calculateTotalPassengers('invalid', '2')).toBe(2);
      expect(calculateTotalPassengers('3', 'invalid')).toBe(3);
    });
  });

  describe('calculateRequiredVehicles', () => {
    it('should calculate vehicles for SUV capacity (5 passengers)', () => {
      const result = calculateRequiredVehicles(3, '5');
      expect(result.requiredVehicles).toBe(1);
      expect(result.isMultipleVehicles).toBe(false);

      const result2 = calculateRequiredVehicles(6, '5');
      expect(result2.requiredVehicles).toBe(2);
      expect(result2.isMultipleVehicles).toBe(true);

      const result3 = calculateRequiredVehicles(12, '5');
      expect(result3.requiredVehicles).toBe(3);
      expect(result3.isMultipleVehicles).toBe(true);
    });

    it('should calculate vehicles for Van capacity (14 passengers)', () => {
      const result = calculateRequiredVehicles(10, '14');
      expect(result.requiredVehicles).toBe(1);
      expect(result.isMultipleVehicles).toBe(false);

      const result2 = calculateRequiredVehicles(15, '14');
      expect(result2.requiredVehicles).toBe(2);
      expect(result2.isMultipleVehicles).toBe(true);

      const result3 = calculateRequiredVehicles(28, '14');
      expect(result3.requiredVehicles).toBe(2);
      expect(result3.isMultipleVehicles).toBe(true);
    });

    it('should handle edge cases', () => {
      const result = calculateRequiredVehicles(0, '5');
      expect(result.requiredVehicles).toBe(1);
      expect(result.isMultipleVehicles).toBe(false);

      const result2 = calculateRequiredVehicles(5, '0');
      expect(result2.requiredVehicles).toBe(1);
      expect(result2.isMultipleVehicles).toBe(false);
    });
  });

  describe('generateServiceNote', () => {
    it('should generate correct notes for single vehicle', () => {
      const calculation = {
        requiredVehicles: 1,
        totalPassengers: 3,
        vehicleCapacity: 5,
        isMultipleVehicles: false
      };

      expect(generateServiceNote(calculation, true)).toBe('Round Trip Rate');
      expect(generateServiceNote(calculation, false)).toBe('One Way Rate');
    });

    it('should generate correct notes for multiple vehicles', () => {
      const calculation = {
        requiredVehicles: 2,
        totalPassengers: 8,
        vehicleCapacity: 5,
        isMultipleVehicles: true
      };

      expect(generateServiceNote(calculation, true)).toBe('2 Vehicle for 8 passengers - Round Trip Rate');
      expect(generateServiceNote(calculation, false)).toBe('2 Vehicle for 8 passengers - One Way Rate');
    });

    it('should generate correct notes for 3+ vehicles', () => {
      const calculation = {
        requiredVehicles: 3,
        totalPassengers: 12,
        vehicleCapacity: 5,
        isMultipleVehicles: true
      };

      expect(generateServiceNote(calculation, true)).toBe('3 Vehicles for 12 passengers - Round Trip Rate');
    });
  });

  describe('calculateServicePrice', () => {
    it('should calculate price for single vehicle', () => {
      const calculation = {
        requiredVehicles: 1,
        totalPassengers: 3,
        vehicleCapacity: 5,
        isMultipleVehicles: false
      };

      expect(calculateServicePrice(100, calculation, false)).toBe(100);
    });

    it('should calculate price for multiple vehicles', () => {
      const calculation = {
        requiredVehicles: 2,
        totalPassengers: 8,
        vehicleCapacity: 5,
        isMultipleVehicles: true
      };

      expect(calculateServicePrice(100, calculation, false)).toBe(200);
    });

    it('should calculate price for airport shuttle (per person)', () => {
      const calculation = {
        requiredVehicles: 1,
        totalPassengers: 3,
        vehicleCapacity: 5,
        isMultipleVehicles: false
      };

      expect(calculateServicePrice(50, calculation, true)).toBe(150);
    });

    it('should handle invalid prices', () => {
      const calculation = {
        requiredVehicles: 2,
        totalPassengers: 8,
        vehicleCapacity: 5,
        isMultipleVehicles: true
      };

      expect(calculateServicePrice(0, calculation, false)).toBe(0);
      expect(calculateServicePrice(-100, calculation, false)).toBe(0);
      expect(calculateServicePrice(NaN, calculation, false)).toBe(0);
    });
  });

  describe('isAirportShuttleService', () => {
    it('should identify airport shuttle by fleet ID', () => {
      expect(isAirportShuttleService('5')).toBe(true);
      expect(isAirportShuttleService(5)).toBe(true);
      expect(isAirportShuttleService('1')).toBe(false);
      expect(isAirportShuttleService(1)).toBe(false);
    });

    it('should identify airport shuttle by service name', () => {
      expect(isAirportShuttleService('1', 'Airport Shuttle Service')).toBe(true);
      expect(isAirportShuttleService('1', 'AIRPORT SHUTTLE')).toBe(true);
      expect(isAirportShuttleService('1', 'SUV Transportation')).toBe(false);
    });
  });

  describe('calculateServiceDetails', () => {
    it('should calculate complete service details for SUV with multiple vehicles', () => {
      const result = calculateServiceDetails('4', '2', '5', 100, true, '1', 'SUV Transportation');
      
      expect(result.totalPassengers).toBe(6);
      expect(result.requiredVehicles).toBe(2);
      expect(result.isMultipleVehicles).toBe(true);
      expect(result.finalPrice).toBe(200);
      expect(result.note).toBe('2 Vehicle for 6 passengers - Round Trip Rate');
      expect(result.isAirportShuttle).toBe(false);
    });

    it('should calculate complete service details for Airport Shuttle', () => {
      const result = calculateServiceDetails('2', '1', '10', 50, false, '5', 'Airport Shuttle');
      
      expect(result.totalPassengers).toBe(3);
      expect(result.requiredVehicles).toBe(1);
      expect(result.isMultipleVehicles).toBe(false);
      expect(result.finalPrice).toBe(150); // 50 * 3 passengers
      expect(result.note).toBe('One Way Rate');
      expect(result.isAirportShuttle).toBe(true);
    });
  });
});
