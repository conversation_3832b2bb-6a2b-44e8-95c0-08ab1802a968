import React from 'react';
import { PayPalButtons, PayPalScriptProvider } from '@paypal/react-paypal-js';
import { usePixel } from '@/shared/hooks';

// Tipos para PayPal
interface PayPalOrderDetails {
  id: string;
  status: string;
  purchase_units: Array<{
    amount: {
      currency_code: string;
      value: string;
    };
    payments: {
      captures: Array<{
        id: string;
        status: string;
        amount: {
          currency_code: string;
          value: string;
        };
      }>;
    };
  }>;
  payer: {
    name: {
      given_name: string;
      surname: string;
    };
    email_address: string;
  };
}

interface PayPalError {
  message: string;
  details?: Array<{
    issue: string;
    description: string;
  }>;
}

interface PayPalButtonProps {
  amount: string;
  currency?: string;
  description?: string;
  onSuccess?: (details: PayPalOrderDetails) => void;
  onError?: (error: PayPalError) => void;
  onCancel?: () => void;
  disabled?: boolean;
  style?: {
    layout?: 'vertical' | 'horizontal';
    color?: 'gold' | 'blue' | 'silver' | 'white' | 'black';
    shape?: 'rect' | 'pill';
    label?: 'paypal' | 'checkout' | 'buynow' | 'pay' | 'installment';
    tagline?: boolean;
    height?: number;
  };
}

const PayPalButton: React.FC<PayPalButtonProps> = ({
  amount,
  currency = 'USD',
  description = 'BajaTravel Transportation Service',
  onSuccess,
  onError,
  onCancel,
  disabled = false,
  style = {
    layout: 'vertical',
    color: 'gold',
    shape: 'rect',
    label: 'pay',
    tagline: false,
    height: 50
  }
}) => {
  const pixel = usePixel();

  // Detectar si estamos en desarrollo local
  const isLocalDevelopment = window.location.hostname === 'localhost' ||
                            window.location.hostname === '127.0.0.1' ||
                            window.location.hostname.includes('localhost');

  // Configuración inicial de PayPal optimizada para desarrollo local
  const initialOptions = {
    clientId: import.meta.env.VITE_PAYPAL_CLIENT_ID || 'test',
    currency: currency,
    intent: 'capture',
    components: 'buttons',
    'enable-funding': 'venmo,paylater',
    'disable-funding': 'credit,card',
    'data-sdk-integration-source': 'developer-studio',
    environment: import.meta.env.VITE_PAYPAL_ENVIRONMENT || 'sandbox',
    // Configuraciones específicas para desarrollo local
    ...(isLocalDevelopment && {
      'data-client-token': undefined, // Evitar token de cliente en desarrollo
      'data-csp-nonce': undefined,    // Evitar CSP nonce en desarrollo
      'data-enable-3ds': false,       // Deshabilitar 3D Secure en desarrollo
      'buyer-country': 'US',          // País por defecto para sandbox
      locale: 'en_US'                 // Locale específico para sandbox
    })
  };

  // Crear orden de PayPal
  const createOrder = (_data: unknown, actions: any) => {
    console.log('Creating PayPal order...', { amount, currency, description });

    try {
      return actions.order.create({
        purchase_units: [
          {
            amount: {
              currency_code: currency,
              value: amount,
            },
            description: description,
          },
        ],
        application_context: {
          shipping_preference: 'NO_SHIPPING', // No necesitamos dirección de envío para servicios
          // Configuraciones adicionales para desarrollo local
          ...(isLocalDevelopment && {
            brand_name: 'BajaTravel',
            landing_page: 'BILLING',
            user_action: 'PAY_NOW',
            return_url: `${window.location.origin}/booking-confirmation`,
            cancel_url: `${window.location.origin}/booking-details`
          })
        },
      });
    } catch (error) {
      console.error('Error creating PayPal order:', error);
      throw error;
    }
  };

  // Aprobar pago
  const onApprove = async (data: unknown, actions: any): Promise<void> => {
    try {
      console.log('PayPal payment approved:', data);

      const details: PayPalOrderDetails = await actions.order.capture();
      console.log('PayPal payment captured:', details);

      // Tracking de Facebook Pixel
      pixel.addPaymentInfo({
        value: parseFloat(amount),
        currency: currency,
        content_type: 'product',
        content_ids: ['transportation_service'],
      });

      pixel.purchase({
        value: parseFloat(amount),
        currency: currency,
        content_type: 'product',
        content_ids: ['transportation_service'],
      });

      // Callback de éxito
      if (onSuccess) {
        onSuccess(details);
      }
    } catch (error) {
      console.error('Error capturing PayPal payment:', error);
      if (onError && error instanceof Error) {
        onError({ message: error.message });
      }
      throw error; // Re-throw para que PayPal maneje el error
    }
  };

  // Manejar errores
  const onErrorHandler = (err: Record<string, unknown>) => {
    console.error('PayPal payment error:', err);
    const error: PayPalError = {
      message: err.message as string || 'PayPal payment error occurred',
      details: err.details as Array<{issue: string; description: string}> || []
    };
    if (onError) {
      onError(error);
    }
  };

  // Manejar cancelación
  const onCancelHandler = () => {
    console.log('PayPal payment cancelled');
    if (onCancel) {
      onCancel();
    }
  };

  // Modo de desarrollo simulado para cuando hay problemas de CORS
  const isDevelopmentMode = import.meta.env.VITE_ENVIRONMENT === 'development';
  const forceSimulation = import.meta.env.VITE_PAYPAL_FORCE_SIMULATION === 'true';
  const simulateInDev = import.meta.env.VITE_PAYPAL_SIMULATE_IN_DEV === 'true';
  const enablePayPalSimulation = forceSimulation || (isDevelopmentMode && isLocalDevelopment && simulateInDev);

  // Función para simular pago de PayPal en desarrollo
  const simulatePayPalPayment = () => {
    console.log('Simulating PayPal payment for development...');

    // Simular datos de PayPal
    const mockPayPalDetails: PayPalOrderDetails = {
      id: `MOCK_ORDER_${Date.now()}`,
      status: 'COMPLETED',
      purchase_units: [{
        amount: {
          currency_code: currency,
          value: amount
        },
        payments: {
          captures: [{
            id: `MOCK_CAPTURE_${Date.now()}`,
            status: 'COMPLETED',
            amount: {
              currency_code: currency,
              value: amount
            }
          }]
        }
      }],
      payer: {
        name: {
          given_name: 'Test',
          surname: 'User'
        },
        email_address: '<EMAIL>'
      }
    };

    // Simular delay de procesamiento
    setTimeout(() => {
      if (onSuccess) {
        onSuccess(mockPayPalDetails);
      }
    }, 2000);
  };

  if (disabled) {
    return (
      <div className="opacity-50 cursor-not-allowed">
        <div className="bg-gray-200 rounded-lg p-4 text-center text-gray-500">
          PayPal payment disabled
        </div>
      </div>
    );
  }

  // Mostrar botón de simulación si hay problemas de CORS en desarrollo
  if (enablePayPalSimulation) {
    return (
      <div className="paypal-simulation-container">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-3">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span className="text-sm font-medium text-yellow-800">Development Mode</span>
          </div>
          <p className="text-xs text-yellow-700 mt-1">
            PayPal simulation enabled for local testing
          </p>
        </div>

        <button
          onClick={simulatePayPalPayment}
          className="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl"
          style={{ height: style?.height || 50 }}
        >
          <div className="flex items-center justify-center space-x-2">
            <span>💳</span>
            <span>Simulate PayPal Payment</span>
            <span className="text-sm opacity-90">(${amount} {currency})</span>
          </div>
        </button>

        <div className="mt-2 text-center">
          <button
            onClick={() => window.location.reload()}
            className="text-xs text-blue-600 hover:text-blue-800 underline"
          >
            Try Real PayPal Instead
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="paypal-button-container">
      <PayPalScriptProvider options={initialOptions}>
        <PayPalButtons
          style={style}
          createOrder={createOrder}
          onApprove={onApprove}
          onError={onErrorHandler}
          onCancel={onCancelHandler}
          disabled={disabled}
        />
      </PayPalScriptProvider>
    </div>
  );
};

export default PayPalButton;
