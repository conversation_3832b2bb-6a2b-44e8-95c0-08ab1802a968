import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useAnalytics } from '@/shared/hooks/analytics';
import { useTheme } from '@/shared/hooks/theme';
import { usePixel } from '@/shared/hooks';
import { BookingForm } from '@/components/booking-form';

const Hero = () => {
  const { trackEvent } = useAnalytics();
  const { theme } = useTheme();
  const pixel = usePixel();
  const layoutType = theme.bookingLayout.defaultLayout;

  const handleViewServices = () => {
    trackEvent({
      action: 'click',
      category: 'Hero CTA',
      label: 'View Services Button'
    });
    pixel.track('ViewContent', { content_name: 'Services Section' });
    document.getElementById('services')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleCallNow = () => {
    trackEvent({
      action: 'click',
      category: 'Hero CTA',
      label: 'Call Now Button'
    });
    pixel.track('Contact', { contact_method: 'Phone' });
  };

  return (
    <section 
      id="home" 
      className="relative min-h-screen flex items-center justify-center"
      style={{
        backgroundImage: `linear-gradient(rgba(0, 100, 152, 0.7), rgba(19, 182, 198, 0.7)), url('/lovable-uploads/70f23d25-1b13-4ee2-8db1-0ade227c89b1.png')`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25px 25px, white 2px, transparent 0)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 py-10">
        {layoutType === 'vertical-right' ? (
          // Layout A: Text left, Booking right
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Text Content */}
            <div className="text-left animate-fade-in">
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 text-shadow">
                BOOK YOUR
                <span className="block text-cabo-sand">AIRPORT</span>
                <span className="block text-3xl md:text-5xl">TRANSPORTATION</span>
              </h1>
              
              <p className="text-xl text-white/90 mb-8 text-shadow">
                Premium transfer service from Los Cabos SJD Airport to your hotel. 
                Luxury vehicles, certified drivers and competitive rates.
              </p>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  size="lg" 
                  className="bg-white text-cabo-blue hover:bg-gray-100 font-semibold px-8 py-3 text-lg"
                  onClick={handleViewServices}
                >
                  View Services
                </Button>
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-cabo-sand to-yellow-400 text-cabo-blue hover:from-yellow-400 hover:to-cabo-sand font-semibold px-8 py-3 text-lg"
                  onClick={handleCallNow}
                >
                  Call Now
                </Button>
              </div>
            </div>

            {/* Booking Form */}
            <div className="animate-slide-up">
              <Card className="p-6 bg-white/95 backdrop-blur-sm shadow-2xl">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">
                  Book Your Transfer
                </h3>
                <BookingForm 
                  variant="vertical"
                  autoNavigate={true}
                  showContactInfo={false}
                  showSpecialOffer={false}
                  searchMode="search"
                />
              </Card>
            </div>
          </div>
        ) : (
          // Layout B: Text centered, Booking horizontal below, space for promotion
          <div className="space-y-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Text Content */}
              <div className="text-left animate-fade-in">
                <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 text-shadow">
                  BOOK YOUR
                  <span className="block text-cabo-sand">AIRPORT</span>
                  <span className="block text-3xl md:text-5xl">TRANSPORTATION</span>
                </h1>
                
                <p className="text-xl text-white/90 mb-8 text-shadow">
                  Premium transfer service from Los Cabos SJD Airport to your hotel. 
                  Luxury vehicles, certified drivers and competitive rates.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Button 
                    size="lg" 
                    className="bg-white text-cabo-blue hover:bg-gray-100 font-semibold px-8 py-3 text-lg"
                    onClick={handleViewServices}
                  >
                    View Services
                  </Button>
                  <Button 
                    size="lg" 
                    className="bg-gradient-to-r from-cabo-sand to-yellow-400 text-cabo-blue hover:from-yellow-400 hover:to-cabo-sand font-semibold px-8 py-3 text-lg"
                    onClick={handleCallNow}
                  >
                    Call Now
                  </Button>
                </div>
              </div>

              {/* Promotion Space */}
              <div className="animate-fade-in">
                <Card className="p-8 bg-gradient-to-r from-cabo-sand to-yellow-400 text-cabo-blue shadow-2xl">
                  <div className="text-center">
                    <h3 className="text-3xl font-bold mb-4">SPECIAL OFFER!</h3>
                    <p className="text-xl mb-4">10% off on your first booking</p>
                    <p className="text-lg font-semibold">Code: WELCOME10</p>
                  </div>
                </Card>
              </div>
            </div>

            {/* Horizontal Booking Form Below */}
            <div className="animate-slide-up flex justify-center">
              <BookingForm 
                variant="horizontal"
                autoNavigate={true}
                showContactInfo={false}
                showSpecialOffer={false}
                searchMode="search"
              />
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default Hero;
