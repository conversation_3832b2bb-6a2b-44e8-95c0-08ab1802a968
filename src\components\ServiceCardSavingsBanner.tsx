import React from 'react';
import { CheckCircleIcon } from 'lucide-react';
import { useBooking } from '@/context/BookingContext';
import { useServiceFleetsRates } from '@/features/services-fleets/hooks/userServiceFleetRates';
import type { ServiceFleetRates } from '@/features/services-fleets/types/service-fleet-rates.types';
import {
  calculateTotalPassengers,
  calculateRequiredVehicles,
  calculateServicePrice,
  isAirportShuttleService
} from '@/utils/vehicleCalculations';

interface ServiceCardSavingsBannerProps {
  serviceId: number;
  currentPrice: number;
  isRoundTrip: boolean;
  serviceName?: string;
  serviceCapacity?: string;
}

const ServiceCardSavingsBanner = ({ serviceId, currentPrice, isRoundTrip, serviceName, serviceCapacity }: ServiceCardSavingsBannerProps) => {
  const { state } = useBooking();

  // Obtener el zone_id del hotel seleccionado
  const zoneId = state.selectedHotel?.zone_id?.toString();

  // Calcular total de pasajeros usando utilidad centralizada
  const getTotalPassengers = () => {
    return calculateTotalPassengers(state.adults, state.kids);
  };

  // Solo hacer la llamada a la API si tenemos zone_id y es round trip
  const shouldFetchRates = Boolean(zoneId && isRoundTrip);

  // Obtener las tarifas para la zona
  const { serviceFleetsRates: rates } = useServiceFleetsRates(
    shouldFetchRates ? zoneId : null
  );

  // Calcular el descuento solo si tenemos datos y es un viaje round trip
  const calculateSavings = () => {
    // debugger;
    if (!rates || !serviceId || !isRoundTrip || !shouldFetchRates) {
      return null;
    }

    // Buscar la tarifa específica para este servicio
    const serviceRate = rates.find((rate: ServiceFleetRates) =>
      rate.fleet_id === serviceId.toString()
    );

    if (!serviceRate) {
      return null;
    }

    let oneWayPrice = Number(serviceRate.one_way.price);
    let roundTripPrice = Number(serviceRate.round_trip.price);

    // Usar utilidades centralizadas para calcular precios
    const totalPassengers = getTotalPassengers();
    const isShuttle = isAirportShuttleService(serviceId, serviceName);

    if (isShuttle) {
      // Para Airport Shuttle, el precio es por persona
      oneWayPrice = calculateServicePrice(oneWayPrice, {
        requiredVehicles: 1,
        totalPassengers,
        vehicleCapacity: parseInt(serviceCapacity || '1'),
        isMultipleVehicles: false
      }, true);
      roundTripPrice = calculateServicePrice(roundTripPrice, {
        requiredVehicles: 1,
        totalPassengers,
        vehicleCapacity: parseInt(serviceCapacity || '1'),
        isMultipleVehicles: false
      }, true);
    } else if (serviceCapacity) {
      // Para otros servicios, calcular vehículos necesarios basado en capacidad
      const calculation = calculateRequiredVehicles(totalPassengers, serviceCapacity);
      oneWayPrice = calculateServicePrice(oneWayPrice, calculation, false);
      roundTripPrice = calculateServicePrice(roundTripPrice, calculation, false);
    }

    // Calcular el precio de dos viajes one way
    const twoOneWayPrice = oneWayPrice * 2;

    // Calcular el ahorro
    const savings = twoOneWayPrice - roundTripPrice;
    const savingsPercentage = Math.round((savings / twoOneWayPrice) * 100);

    // Solo mostrar si hay ahorro significativo (más de $5 o 5%)
    if (savings > 5 && savingsPercentage > 0) {
      return {
        oneWayPrice,
        twoOneWayPrice,
        roundTripPrice,
        savings,
        savingsPercentage
      };
    }

    return null;
  };

  const savingsData = calculateSavings();

  // console.log({savingsData}, {isRoundTrip}, {shouldFetchRates});
  // No mostrar el banner si no hay datos de ahorro, no es round trip, o no tenemos zone_id
  if (!savingsData || !isRoundTrip || !shouldFetchRates) {
    return null;
  }

  return (
    <div className="mt-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-200 rounded-lg">
      <div className="flex items-center gap-2 mb-1">
        <CheckCircleIcon className="w-4 h-4 text-green-600" />
        <span className="text-green-700 font-semibold text-sm">
          ✓ Best Price
        </span>
      </div>
      
      <div className="space-y-1">
        <div className="text-xs text-gray-600">
          <span className="line-through">
            ${savingsData.twoOneWayPrice.toFixed(2)}
          </span>
          <span className="ml-2 text-green-600 font-medium">
            Save ${savingsData.savings.toFixed(2)} ({savingsData.savingsPercentage}% off)
          </span>
        </div>
        
        <div className="text-xs text-gray-500">
          vs. booking two One-Way trips
        </div>
      </div>
    </div>
  );
};

export default ServiceCardSavingsBanner;
