import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PlaneIcon, UsersIcon, CarIcon, PlusIcon, CheckCircleIcon } from 'lucide-react';
import { useBooking } from '@/context/BookingContext';
import { format } from 'date-fns';
import RoundTripSavingsBanner from './RoundTripSavingsBanner';
import {
  calculateTotalTransportationPrice,
  formatPrice,
  generateAdditionalItemsSummary
} from '@/utils/additionalItemsCalculations';
import { useState } from 'react';

interface ServiceData {
  id: number;
  name: string;
  type: string;
  price: number;
  image?: string;
}

interface TransferBookingCardProps {
  serviceData?: ServiceData;
  onModifyBooking: () => void;
}

const TransferBookingCard = ({ serviceData, onModifyBooking }: TransferBookingCardProps) => {
  const { state } = useBooking();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Calcular precio total incluyendo servicios adicionales
  const calculateTotalPrice = () => {
    if (!serviceData) return null;

    return calculateTotalTransportationPrice(
      serviceData.price,
      state.additionalItems
    );
  };

  const priceCalculation = calculateTotalPrice();
  const additionalItemsSummary = generateAdditionalItemsSummary(state.additionalItems);

  // Formatear fecha para mostrar
  const formatDate = (date?: Date) => {
    if (!date) return 'Not selected';
    return format(date, 'MMM dd, yyyy');
  };

  // Obtener nombre del aeropuerto de origen
  const getFromAirportDisplay = () => {
    if (state.fromAirportInfo) {
      return `${state.fromAirportInfo.name} (${state.fromAirportInfo.code})`;
    }
    return state.from || 'Not selected';
  };

  // Obtener nombre del destino
  const getToDisplay = () => {
    if (state.selectedHotel) {
      return state.selectedHotel.name;
    }
    return state.to || 'Not selected';
  };

  // Calcular total de pasajeros
  const getTotalPassengers = () => {
    const adults = parseInt(state.adults) || 0;
    const kids = parseInt(state.kids) || 0;
    return adults + kids;
  };

  // Calcular número total de servicios contratados
  const getTotalServices = () => {
    let serviceCount = 0;

    // Servicio base (siempre 1 o 2 dependiendo de si es round trip)
    serviceCount += state.roundTrip ? 2 : 1;

    // Servicios adicionales
    if (state.additionalItems?.extraServices?.stopShop) serviceCount++;
    if (state.additionalItems?.extraServices?.golfBags) serviceCount++;
    if (state.additionalItems?.extraServices?.surfboards) serviceCount++;

    return serviceCount;
  };

  // Obtener lista detallada de servicios
  const getServicesList = () => {
    const services = [];

    // Servicios de transporte base
    if (state.roundTrip) {
      services.push({
        name: 'Airport to Hotel Transfer',
        type: 'transportation',
        icon: '✈️',
        included: true
      });
      services.push({
        name: 'Hotel to Airport Transfer',
        type: 'transportation',
        icon: '🏨',
        included: true
      });
    } else {
      services.push({
        name: 'One-Way Transfer',
        type: 'transportation',
        icon: '🚗',
        included: true
      });
    }

    // Servicios adicionales
    if (state.additionalItems?.extraServices?.stopShop) {
      services.push({
        name: 'Grocery Stop',
        type: 'additional',
        icon: '🛒',
        cost: 25.00,
        included: false
      });
    }

    if (state.additionalItems?.extraServices?.golfBags) {
      services.push({
        name: 'Golf Clubs/Bags Transport',
        type: 'additional',
        icon: '⛳',
        cost: 30.00,
        included: false
      });
    }

    if (state.additionalItems?.extraServices?.surfboards) {
      services.push({
        name: 'Surfboards Transport',
        type: 'additional',
        icon: '🏄',
        cost: 20.00,
        included: false
      });
    }

    return services;
  };

  // Calcular precio de comparación y descuento para el banner Best Price
  const getBestPriceInfo = () => {
    if (!priceCalculation) return null;

    let comparisonPrice = 0;
    let comparisonText = '';
    let shouldShowBanner = false;

    if (state.roundTrip) {
      // Para round trip: comparar con dos viajes one-way separados
      comparisonPrice = priceCalculation.basePrice * 1.25; // 25% más caro por separado
      comparisonText = 'vs. booking two One-Way trips';
      shouldShowBanner = true;
    } else if (additionalItemsSummary.length > 0) {
      // Para one-way con servicios adicionales: comparar con precio sin bundle
      comparisonPrice = priceCalculation.totalPrice * 1.12; // 12% más caro sin bundle
      comparisonText = 'vs. booking services separately';
      shouldShowBanner = true;
    } else if (getTotalPassengers() >= 4) {
      // Para grupos grandes: comparar con múltiples taxis
      comparisonPrice = priceCalculation.totalPrice * 1.18; // 18% más caro con taxis
      comparisonText = 'vs. multiple taxi rides';
      shouldShowBanner = true;
    }

    if (!shouldShowBanner) return null;

    const savings = comparisonPrice - priceCalculation.totalPrice;
    const discountPercentage = Math.round((savings / comparisonPrice) * 100);

    return {
      originalPrice: comparisonPrice,
      currentPrice: priceCalculation.totalPrice,
      savings: savings,
      discountPercentage: discountPercentage,
      comparisonText: comparisonText
    };
  };

  return (
    <Card className="bg-white shadow-lg border-0 overflow-hidden group">
      <CardHeader className="bg-white border-b pb-4">
        <CardTitle className="flex items-center gap-2 text-primary text-lg">
          <PlaneIcon className="w-5 h-5" />
          Transfer Booking Details
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Transfer Details Section */}
        <div className="space-y-6">
          {/* Arriving Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-primary">
              <PlaneIcon className="w-4 h-4 rotate-45" />
              <span className="font-semibold text-sm">Arriving</span>
            </div>
            <div className="space-y-2 pl-6">
              <div className="text-sm">
                <span className="font-medium">From:</span>
                <span className="ml-2 text-muted-foreground">{getFromAirportDisplay()}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">To:</span>
                <span className="ml-2 text-muted-foreground">{getToDisplay()}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Date:</span>
                <span className="ml-2 text-muted-foreground">{formatDate(state.date)}</span>
              </div>
              <div className="text-sm">
                <span className="font-medium">Time:</span>
                {state.time ? (
                  <span className="ml-2 text-muted-foreground">{state.time}</span>
                ) : (
                  <span className="ml-2 text-xs text-amber-600 italic">Select time in Flight Information</span>
                )}
              </div>
            </div>
          </div>

          {/* Departing Section - Only show if roundTrip is true */}
          {state.roundTrip && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-primary">
                <PlaneIcon className="w-4 h-4 -rotate-45" />
                <span className="font-semibold text-sm">Departing</span>
              </div>
              <div className="space-y-2 pl-6">
                <div className="text-sm">
                  <span className="font-medium">From:</span>
                  <span className="ml-2 text-muted-foreground">{getToDisplay()}</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium">To:</span>
                  <span className="ml-2 text-muted-foreground">{getFromAirportDisplay()}</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium">Date:</span>
                  <span className="ml-2 text-muted-foreground">{formatDate(state.returnDate)}</span>
                </div>
                <div className="text-sm">
                  <span className="font-medium">Time:</span>
                  {state.returnTime ? (
                    <span className="ml-2 text-muted-foreground">{state.returnTime}</span>
                  ) : (
                    <span className="ml-2 text-xs text-amber-600 italic">Select time in Flight Information</span>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Passengers Section */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-primary">
            <UsersIcon className="w-4 h-4" />
            <span className="font-semibold text-sm">Passengers</span>
          </div>
          <div className="pl-6 space-y-1">
            <div className="text-sm">
              <span className="font-medium">Adults:</span>
              <span className="ml-2 text-muted-foreground">{state.adults}</span>
            </div>
            {parseInt(state.kids) > 0 && (
              <div className="text-sm">
                <span className="font-medium">Kids:</span>
                <span className="ml-2 text-muted-foreground">{state.kids}</span>
              </div>
            )}
            <div className="text-sm font-medium">
              <span className="font-medium">Total:</span>
              <span className="ml-2 text-primary">{getTotalPassengers()} passengers</span>
            </div>
          </div>
        </div>

        {/* Services Summary Section */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-primary">
              <CarIcon className="w-4 h-4" />
              <span className="font-semibold text-sm">Services Contracted</span>
            </div>
            <Badge variant="secondary" className="bg-primary/10 text-primary">
              {getTotalServices()} {getTotalServices() === 1 ? 'Service' : 'Services'}
            </Badge>
          </div>

          <div className="pl-6 space-y-2">
            {getServicesList().map((service, index) => (
              <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <span className="text-lg">{service.icon}</span>
                  <div>
                    <div className="text-sm font-medium">{service.name}</div>
                    {service.type === 'transportation' && (
                      <div className="text-xs text-green-600 flex items-center gap-1">
                        <CheckCircleIcon className="w-3 h-3" />
                        Included in base price
                      </div>
                    )}
                  </div>
                </div>
                {service.cost && (
                  <div className="text-sm font-medium text-primary">
                    +${service.cost.toFixed(2)}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Service Details Section */}
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-primary">
            <span className="text-yellow-500">🚗</span>
            <span className="font-semibold text-sm">Vehicle Details</span>
          </div>
          <div className="pl-6">
            <div className="flex items-start gap-3">
              {/* Vehicle Image Thumbnail */}
              <div className="w-16 h-12 bg-muted rounded-lg overflow-hidden flex items-center justify-center">
                {serviceData?.image ? (
                  <>
                    {/* Skeleton loader while image loads */}
                    {!imageLoaded && (
                      <div className="w-full h-full bg-gray-200 animate-pulse flex items-center justify-center">
                        <span className="text-xs text-gray-400">🚙</span>
                      </div>
                    )}
                    {/* Vehicle Image */}
                    <img
                      src={serviceData.image}
                      alt={serviceData.name}
                      className={`w-full h-full object-cover transition-opacity duration-300 ${
                        !imageLoaded ? 'opacity-0 absolute' : 'opacity-100'
                      }`}
                      onLoad={() => setImageLoaded(true)}
                      onError={() => {
                        setImageError(true);
                        setImageLoaded(false);
                      }}
                      loading="lazy"
                    />
                    {/* Fallback if image fails to load */}
                    {imageError && (
                      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                        <span className="text-xs text-gray-400">🚙</span>
                      </div>
                    )}
                  </>
                ) : (
                  <span className="text-xs">🚙</span>
                )}
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm">
                  {state.roundTrip ? 'Round Trip' : 'One Way'}
                </div>
                <div className="text-xs text-muted-foreground">Private Service</div>
                <div className="text-xs text-muted-foreground">
                  {serviceData?.type || 'SUV Transportation'}
                </div>
                {state.selectedHotel && (
                  <div className="text-xs text-muted-foreground mt-1">
                    Zone: {state.selectedHotel.zone_name || `Zone ${state.selectedHotel.zone_id}`}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Modify Booking Button */}
        <div className="pt-4">
          <Button 
            variant="outline"
            className="w-full border-primary text-primary hover:bg-primary hover:text-white transition-colors"
            onClick={onModifyBooking}
          >
            <span className="mr-2">✏️</span>
            Modify Booking
          </Button>
        </div>

        {/* Best Price Banner */}
        {serviceData && priceCalculation && getBestPriceInfo() && (
          <div className="relative p-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg shadow-sm overflow-hidden">
            {/* Background pattern for visual appeal */}
            <div className="absolute top-0 right-0 w-16 h-16 bg-green-100 rounded-full -translate-y-8 translate-x-8 opacity-30"></div>
            <div className="absolute bottom-0 left-0 w-12 h-12 bg-emerald-100 rounded-full translate-y-6 -translate-x-6 opacity-20"></div>

            <div className="relative z-10">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-sm">
                    <CheckCircleIcon className="w-4 h-4 text-white" />
                  </div>
                  <span className="font-semibold text-green-800">✓ Best Price</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span className="line-through text-gray-500 text-xs">
                    ${getBestPriceInfo()?.originalPrice.toFixed(2)}
                  </span>
                  <span className="font-semibold text-green-700 bg-green-100 px-2 py-1 rounded-full text-xs">
                    Save ${getBestPriceInfo()?.savings.toFixed(2)} ({getBestPriceInfo()?.discountPercentage}% off)
                  </span>
                </div>
              </div>
              <div className="text-xs text-green-700 mt-1 ml-8 font-medium">
                {getBestPriceInfo()?.comparisonText}
              </div>
            </div>
          </div>
        )}

        {/* Service Summary & Price Breakdown */}
        {serviceData && priceCalculation && (
          <div className="p-4 bg-gradient-to-r from-primary/10 to-primary/5 rounded-lg border border-primary/20">
            <div className="space-y-4">
              {/* Service Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-lg">{serviceData.name}</h3>
                  <p className="text-sm text-muted-foreground">{serviceData.type}</p>
                </div>
                <Badge variant="outline" className="bg-white/50">
                  {state.roundTrip ? 'Round Trip' : 'One Way'}
                </Badge>
              </div>

              {/* Detailed Price Breakdown */}
              <div className="space-y-3">
                <div className="text-sm font-medium text-gray-700 mb-2">Price Breakdown:</div>

                {/* Base Transportation Services */}
                <div className="space-y-2">
                  {state.roundTrip ? (
                    <>
                      <div className="flex justify-between items-center text-sm bg-white/30 p-2 rounded">
                        <div className="flex items-center gap-2">
                          <span>✈️</span>
                          <span>Airport to Hotel Transfer</span>
                        </div>
                        <span className="font-medium">{formatPrice(priceCalculation.basePrice / 2)}</span>
                      </div>
                      <div className="flex justify-between items-center text-sm bg-white/30 p-2 rounded">
                        <div className="flex items-center gap-2">
                          <span>🏨</span>
                          <span>Hotel to Airport Transfer</span>
                        </div>
                        <span className="font-medium">{formatPrice(priceCalculation.basePrice / 2)}</span>
                      </div>
                    </>
                  ) : (
                    <div className="flex justify-between items-center text-sm bg-white/30 p-2 rounded">
                      <div className="flex items-center gap-2">
                        <span>🚗</span>
                        <span>One-Way Transfer</span>
                      </div>
                      <span className="font-medium">{formatPrice(priceCalculation.basePrice)}</span>
                    </div>
                  )}
                </div>

                {/* Additional Services */}
                {additionalItemsSummary.length > 0 && (
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-gray-600 mt-3 mb-1">Additional Services:</div>
                    {getServicesList()
                      .filter(service => service.type === 'additional')
                      .map((service, index) => (
                        <div key={index} className="flex justify-between items-center text-sm bg-blue-50 p-2 rounded">
                          <div className="flex items-center gap-2">
                            <span>{service.icon}</span>
                            <span>{service.name}</span>
                          </div>
                          <span className="font-medium text-blue-700">+{formatPrice(service.cost || 0)}</span>
                        </div>
                      ))
                    }
                  </div>
                )}

                {/* Subtotal if there are additional services */}
                {additionalItemsSummary.length > 0 && (
                  <div className="flex justify-between items-center text-sm pt-2 border-t border-white/30">
                    <span className="font-medium">Subtotal</span>
                    <span className="font-medium">{formatPrice(priceCalculation.totalPrice)}</span>
                  </div>
                )}

                {/* Total Price */}
                <div className="flex justify-between items-center pt-3 border-t-2 border-primary/30">
                  <div>
                    <span className="text-lg font-bold">Total Price</span>
                    <div className="text-xs text-muted-foreground">
                      {getTotalServices()} {getTotalServices() === 1 ? 'service' : 'services'} • {getTotalPassengers()} passengers
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">{formatPrice(priceCalculation.totalPrice)}</div>
                    <div className="text-xs text-muted-foreground">USD</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        
        {/* Round Trip Savings Banner */}
        <RoundTripSavingsBanner serviceData={serviceData} />
        
        {/* Free Cancellation Banner */}
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2 mb-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-sm font-medium text-blue-900">Free Cancellation</span>
          </div>
          <p className="text-xs text-blue-700">Cancel up to 24 hours before your trip</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransferBookingCard;
