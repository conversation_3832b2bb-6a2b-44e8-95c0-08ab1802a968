import { Select, SelectContent, <PERSON>Item, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface TimeSelectProps {
  value: string;
  onChange: (value: string) => void;
  error?: boolean;
  className?: string;
  errorText?: string;
  label?: string;
  required?: boolean;
  placeholder?: string;
}

export const TimeSelect = ({
  value,
  onChange,
  error,
  className,
  errorText,
  label = "Select Time"
}: TimeSelectProps) => {
  const timeOptions = [
    { value: "06:00", label: "6:00 AM" },
    { value: "07:00", label: "7:00 AM" },
    { value: "08:00", label: "8:00 AM" },
    { value: "09:00", label: "9:00 AM" },
    { value: "10:00", label: "10:00 AM" },
    { value: "11:00", label: "11:00 AM" },
    { value: "12:00", label: "12:00 PM" },
    { value: "13:00", label: "1:00 PM" },
    { value: "14:00", label: "2:00 PM" },
    { value: "15:00", label: "3:00 PM" },
    { value: "16:00", label: "4:00 PM" },
    { value: "17:00", label: "5:00 PM" },
    { value: "18:00", label: "6:00 PM" },
    { value: "19:00", label: "7:00 PM" },
    { value: "20:00", label: "8:00 PM" },
    { value: "21:00", label: "9:00 PM" },
    { value: "22:00", label: "10:00 PM" },
    { value: "23:00", label: "11:00 PM" },
  ];

  return (
    <div className="w-full">
      <Select value={value || ""} onValueChange={onChange}>
        <SelectTrigger className={cn("h-12", error && "border-red-500", className)}>
          {value ? (
            <SelectValue />
          ) : (
            <span className="text-muted-foreground">Select time</span>
          )}
        </SelectTrigger>
        <SelectContent className="bg-white z-50">
          {timeOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      {/* Reserve space for error message to prevent layout shift */}
      <div className="h-5 mt-1">
        {error && <p className="text-red-500 text-xs">{errorText || "Required"}</p>}
      </div>
    </div>
  );
};
