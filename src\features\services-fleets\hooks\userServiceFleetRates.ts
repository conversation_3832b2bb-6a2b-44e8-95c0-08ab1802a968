import { useQuery } from '@tanstack/react-query';
import type { ServiceFleetRates } from '../types/service-fleet-rates.types';
import { fetchFleetsRatesByZone } from '../services/services-fleet-rates.service';
import { useEffect } from 'react';

export const useServiceFleetsRates = (zoneId: string | null) => {
    // Convertir y validar zoneId
    const validZoneId = zoneId ? Number(zoneId) : null;
    const isValidZoneId = validZoneId !== null && !isNaN(validZoneId) && validZoneId > 0;

    const {
        data: serviceFleetsRates,
        isLoading: loading,
        error,
        refetch
    } = useQuery<ServiceFleetRates[], Error>({
        queryKey: ['serviceFleetsRates', validZoneId],
        queryFn: () => {
            if (!isValidZoneId) {
                throw new Error('Invalid zone ID');
            }
            return fetchFleetsRatesByZone(validZoneId);
        },
        enabled: isValidZoneId,
        retry: 2,
        staleTime: 1000 * 60 * 5, // Datos considerados frescos por 5 minutos
        gcTime: 1000 * 60 * 30, // Mantener en caché por 30 minutos
    });

    // Efecto para refrescar datos cuando el zoneId cambia y es válido
    useEffect(() => {
        if (isValidZoneId) {
            refetch();
        }
    }, [isValidZoneId, refetch]);

    return {
        serviceFleetsRates: serviceFleetsRates ?? null,
        loading,
        error: error ? error.message : null,
        isValidZoneId
    };
};