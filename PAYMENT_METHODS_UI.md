# Payment Methods UI Documentation

## Overview

La nueva interfaz de métodos de pago utiliza Tabs de shadcn/ui para proporcionar una experiencia de usuario moderna y escalable. Permite agregar fácilmente nuevos métodos de pago en el futuro.

## Características

### ✅ **Implementado:**
- **Payment Cash** - Pago en efectivo al llegar
- **PayPal** - Pago seguro con PayPal
- **Interfaz con Tabs** - Navegación intuitiva entre métodos
- **Badges informativos** - "Popular", "Secure", etc.
- **Diseño responsive** - Funciona en móviles y desktop
- **Estados de loading** - Feedback visual durante procesamiento

### 🔄 **Preparado para futuro:**
- **Credit Card** - Marcado como "Coming Soon"
- **Bank Transfer** - Fácil de agregar
- **Crypto payments** - Estructura extensible
- **Buy now, pay later** - Servicios como Klarna, Afterpay

## Estructura de Componentes

### **CheckoutButton.tsx**
```typescript
// Tipos de métodos de pago
type PaymentMethod = 'cash' | 'paypal' | 'credit-card' | 'bank-transfer';

interface PaymentMethodInfo {
  id: PaymentMethod;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  enabled: boolean;
  badge?: string;
}
```

### **Configuración de Métodos**
```typescript
const paymentMethods: PaymentMethodInfo[] = [
  {
    id: 'cash',
    name: 'Payment Cash',
    description: 'Pay in cash upon arrival',
    icon: DollarSign,
    enabled: true,
    badge: 'Popular'
  },
  {
    id: 'paypal',
    name: 'PayPal',
    description: 'Pay securely with PayPal',
    icon: Wallet,
    enabled: enablePayPal && parseFloat(amount) > 0,
    badge: 'Secure'
  },
  {
    id: 'credit-card',
    name: 'Credit Card',
    description: 'Pay with credit or debit card',
    icon: CreditCard,
    enabled: false, // Deshabilitado por ahora
    badge: 'Coming Soon'
  }
];
```

## Flujo de Pagos

### **1. Pago en Efectivo**
```mermaid
sequenceDiagram
    participant U as Usuario
    participant F as Frontend
    participant API as Retails API

    U->>F: Selecciona "Payment Cash"
    U->>F: Confirma reserva
    F->>F: Genera transaction_id
    F->>F: Marca payment_method = "Cash"
    F->>API: Envía datos con status "pending"
    API->>F: Confirmación
    F->>U: Página de confirmación
```

### **2. Pago con PayPal**
```mermaid
sequenceDiagram
    participant U as Usuario
    participant F as Frontend
    participant P as PayPal
    participant API as Retails API

    U->>F: Selecciona "PayPal"
    F->>P: Inicia proceso de pago
    P->>U: Procesa pago
    P->>F: Confirmación + transaction_id
    F->>F: Transforma datos
    F->>API: Envía datos con status "completed"
    API->>F: Confirmación
    F->>U: Página de confirmación
```

## Diseño Visual

### **Tabs Layout**
```
┌─────────────────────────────────────────┐
│           Choose Payment Method         │
│    Select how you'd like to pay for     │
│        your transportation service      │
├─────────────────────────────────────────┤
│  [💰 Payment Cash] [💳 PayPal]         │
│     Popular           Secure            │
├─────────────────────────────────────────┤
│                                         │
│  [Contenido del método seleccionado]    │
│                                         │
│  • Descripción del método               │
│  • Total amount: $150.00 USD            │
│  • Botón de acción                      │
│                                         │
└─────────────────────────────────────────┘
```

### **Cash Payment Tab**
- **Color scheme:** Verde (green-50, green-600, etc.)
- **Icon:** DollarSign de Lucide React
- **Features:**
  - Descripción clara del proceso
  - Monto total destacado
  - "No additional fees" badge
  - Botón "Confirm Cash Payment"

### **PayPal Tab**
- **Color scheme:** Azul (blue-50, blue-600, etc.)
- **Icon:** Wallet de Lucide React
- **Features:**
  - Integración completa con PayPal SDK
  - Botón oficial de PayPal
  - "Secure payment processing" badge
  - Monto total destacado

## Agregar Nuevos Métodos de Pago

### **1. Definir el nuevo tipo**
```typescript
type PaymentMethod = 'cash' | 'paypal' | 'credit-card' | 'bank-transfer' | 'stripe';
```

### **2. Agregar a la configuración**
```typescript
{
  id: 'stripe',
  name: 'Credit Card',
  description: 'Pay with Stripe',
  icon: CreditCard,
  enabled: true,
  badge: 'New'
}
```

### **3. Crear el TabsContent**
```typescript
<TabsContent value="stripe" className="space-y-4">
  <Card className="border-purple-200 bg-purple-50">
    <CardContent className="pt-6">
      {/* Contenido específico de Stripe */}
      <StripePaymentForm 
        amount={amount}
        onSuccess={handleStripeSuccess}
        onError={handleStripeError}
      />
    </CardContent>
  </Card>
</TabsContent>
```

### **4. Agregar handler**
```typescript
const handleStripeSuccess = (details: any) => {
  // Lógica específica de Stripe
};
```

## Personalización

### **Colores por Método**
- **Cash:** Verde (confianza, dinero)
- **PayPal:** Azul (seguridad, tecnología)
- **Credit Card:** Púrpura (premium, elegancia)
- **Bank Transfer:** Gris (formal, bancario)

### **Iconos Disponibles**
- `DollarSign` - Efectivo
- `Wallet` - PayPal, wallets digitales
- `CreditCard` - Tarjetas de crédito/débito
- `Building2` - Transferencias bancarias
- `Smartphone` - Pagos móviles
- `Bitcoin` - Criptomonedas

### **Badges Sugeridos**
- "Popular" - Método más usado
- "Secure" - Métodos seguros
- "Fast" - Procesamiento rápido
- "New" - Métodos recién agregados
- "Coming Soon" - En desarrollo
- "Recommended" - Recomendado por la empresa

## Responsive Design

### **Desktop (md+)**
- Tabs horizontales
- Cards con padding amplio
- Botones de tamaño grande

### **Mobile (sm)**
- Tabs apilados si es necesario
- Cards compactos
- Botones full-width

## Accesibilidad

- **Keyboard navigation** - Tabs navegables con teclado
- **Screen readers** - Labels descriptivos
- **Color contrast** - Cumple WCAG 2.1
- **Focus indicators** - Estados de focus visibles

## Testing

### **Casos de Prueba**
1. Selección de método de pago
2. Validación de formularios
3. Procesamiento de pagos
4. Manejo de errores
5. Estados de loading
6. Navegación entre tabs
7. Responsive behavior

### **Datos de Prueba**
- Montos válidos e inválidos
- Información de contacto completa/incompleta
- Errores de red
- Timeouts de PayPal
- Diferentes tamaños de pantalla
