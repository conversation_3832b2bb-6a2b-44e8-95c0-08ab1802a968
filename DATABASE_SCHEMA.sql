-- Tabla para guardar reservas con pagos de PayPal
-- Ejecutar en tu base de datos de CodeIgniter

CREATE TABLE `paypal_bookings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  
  -- Información de contacto
  `contact_name` varchar(255) NOT NULL,
  `contact_email` varchar(255) NOT NULL,
  `contact_phone` varchar(50) DEFAULT NULL,
  `contact_whatsapp` varchar(50) DEFAULT NULL,
  
  -- Información de vuelo
  `flight_number` varchar(50) DEFAULT NULL,
  `airline` varchar(100) DEFAULT NULL,
  `arrival_date` date DEFAULT NULL,
  `arrival_time` time DEFAULT NULL,
  `departure_date` date DEFAULT NULL,
  `departure_time` time DEFAULT NULL,
  
  -- Servicios adicionales
  `baby_seats` int(2) DEFAULT 0,
  `car_seats` int(2) DEFAULT 0,
  `booster_seats` int(2) DEFAULT 0,
  `special_instructions` text DEFAULT NULL,
  `extra_services` json DEFAULT NULL,
  
  -- Información del servicio
  `service_id` int(11) DEFAULT NULL,
  `service_name` varchar(255) DEFAULT NULL,
  `service_type` varchar(100) DEFAULT NULL,
  `base_price` decimal(10,2) DEFAULT 0.00,
  
  -- Información de pago PayPal
  `payment_method` varchar(50) NOT NULL DEFAULT 'paypal',
  `paypal_transaction_id` varchar(255) NOT NULL,
  `paypal_capture_id` varchar(255) DEFAULT NULL,
  `payment_status` varchar(50) NOT NULL DEFAULT 'pending',
  `total_amount` decimal(10,2) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'USD',
  `payer_email` varchar(255) DEFAULT NULL,
  `payer_name` varchar(255) DEFAULT NULL,
  
  -- Estado de la reserva
  `booking_status` varchar(50) NOT NULL DEFAULT 'pending',
  
  -- Metadatos
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Datos completos como JSON para referencia
  `raw_booking_data` longtext DEFAULT NULL,
  `raw_payment_data` text DEFAULT NULL,
  
  PRIMARY KEY (`id`),
  UNIQUE KEY `paypal_transaction_id` (`paypal_transaction_id`),
  KEY `contact_email` (`contact_email`),
  KEY `payment_status` (`payment_status`),
  KEY `booking_status` (`booking_status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Índices adicionales para optimización
CREATE INDEX `idx_booking_date` ON `paypal_bookings` (`arrival_date`, `departure_date`);
CREATE INDEX `idx_service` ON `paypal_bookings` (`service_id`, `service_type`);
CREATE INDEX `idx_payment_lookup` ON `paypal_bookings` (`paypal_transaction_id`, `payment_status`);

-- Vista para consultas simplificadas (opcional)
CREATE VIEW `booking_summary` AS
SELECT 
    id,
    contact_name,
    contact_email,
    contact_phone,
    service_name,
    total_amount,
    currency,
    payment_status,
    booking_status,
    paypal_transaction_id,
    created_at
FROM paypal_bookings
ORDER BY created_at DESC;
