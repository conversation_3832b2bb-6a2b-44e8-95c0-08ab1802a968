import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MailIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Control, Controller, FieldErrors } from 'react-hook-form';
import type { BookingDetailsFormData } from '../schemas/bookingDetailsSchema';

interface ContactInformationCardProps {
  control: Control<BookingDetailsFormData>;
  errors: FieldErrors<BookingDetailsFormData>;
}

const ContactInformationCard = ({ control, errors }: ContactInformationCardProps) => {
  return (
    <Card className="bg-white shadow-lg border-0 overflow-hidden group">
      <CardHeader className="bg-gradient-to-r from-primary/10 to-primary/5 border-b border-primary/20">
        <CardTitle className="flex items-center gap-2 text-primary">
          <MailIcon className="w-5 h-5" />
          Contact Information
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-4">
        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>First Name</Label>
            <Controller
              name="contactInfo.firstName"
              control={control}
              render={({ field }) => (
                <Input
                  placeholder="Write your first name"
                  className={cn(errors.contactInfo?.firstName && "border-destructive")}
                  {...field}
                />
              )}
            />
            {errors.contactInfo?.firstName && (
              <p className="text-destructive text-xs">{errors.contactInfo.firstName.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label>Last Name</Label>
            <Controller
              name="contactInfo.lastName"
              control={control}
              render={({ field }) => (
                <Input
                  placeholder="Write your last name"
                  className={cn(errors.contactInfo?.lastName && "border-destructive")}
                  {...field}
                />
              )}
            />
            {errors.contactInfo?.lastName && (
              <p className="text-destructive text-xs">{errors.contactInfo.lastName.message}</p>
            )}
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Email</Label>
            <Controller
              name="contactInfo.email"
              control={control}
              render={({ field }) => (
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  className={cn(errors.contactInfo?.email && "border-destructive")}
                  {...field}
                />
              )}
            />
            {errors.contactInfo?.email && (
              <p className="text-destructive text-xs">{errors.contactInfo.email.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label>Confirm your email</Label>
            <Controller
              name="contactInfo.confirmEmail"
              control={control}
              render={({ field }) => (
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  className={cn(errors.contactInfo?.confirmEmail && "border-destructive")}
                  {...field}
                />
              )}
            />
            {errors.contactInfo?.confirmEmail && (
              <p className="text-destructive text-xs">{errors.contactInfo.confirmEmail.message}</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label>Mobile Phone Number</Label>
          <div className="flex gap-2">
            <Controller
              name="contactInfo.countryCode"
              control={control}
              render={({ field }) => (
                <Select value={field.value} onValueChange={field.onChange}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="US">🇺🇸 +1</SelectItem>
                    <SelectItem value="MX">🇲🇽 +52</SelectItem>
                    <SelectItem value="CA">🇨🇦 +1</SelectItem>
                  </SelectContent>
                </Select>
              )}
            />
            <Controller
              name="contactInfo.phone"
              control={control}
              render={({ field }) => (
                <Input
                  placeholder="(*************"
                  className={cn("flex-1", errors.contactInfo?.phone && "border-destructive")}
                  {...field}
                />
              )}
            />
          </div>
          {errors.contactInfo?.phone && (
            <p className="text-destructive text-xs">{errors.contactInfo.phone.message}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ContactInformationCard;
