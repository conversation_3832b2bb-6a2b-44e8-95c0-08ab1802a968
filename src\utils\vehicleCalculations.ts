/**
 * Utilidades para cálculos de vehículos y servicios de transporte
 * Centraliza la lógica de cálculo de número de vehículos necesarios
 * basado en capacidad y número de pasajeros
 */

export interface VehicleCalculationResult {
  requiredVehicles: number;
  totalPassengers: number;
  vehicleCapacity: number;
  isMultipleVehicles: boolean;
}

/**
 * Calcula el número total de pasajeros (adultos + niños)
 */
export const calculateTotalPassengers = (adults: string | number, kids: string | number): number => {
  const adultsCount = typeof adults === 'string' ? parseInt(adults) || 0 : adults || 0;
  const kidsCount = typeof kids === 'string' ? parseInt(kids) || 0 : kids || 0;
  return adultsCount + kidsCount;
};

/**
 * Calcula el número de vehículos necesarios basado en capacidad y pasajeros
 */
export const calculateRequiredVehicles = (
  totalPassengers: number, 
  vehicleCapacity: string | number
): VehicleCalculationResult => {
  const capacity = typeof vehicleCapacity === 'string' ? parseInt(vehicleCapacity) || 1 : vehicleCapacity || 1;
  
  // Si no hay pasajeros o capacidad inválida, retornar 1 vehículo
  if (totalPassengers <= 0 || capacity <= 0) {
    return {
      requiredVehicles: 1,
      totalPassengers,
      vehicleCapacity: capacity,
      isMultipleVehicles: false
    };
  }
  
  // Calcular número de vehículos necesarios (redondear hacia arriba)
  const requiredVehicles = Math.ceil(totalPassengers / capacity);
  
  return {
    requiredVehicles,
    totalPassengers,
    vehicleCapacity: capacity,
    isMultipleVehicles: requiredVehicles > 1
  };
};

/**
 * Genera una nota descriptiva para el servicio basado en el número de vehículos
 */
export const generateServiceNote = (
  calculation: VehicleCalculationResult,
  isRoundTrip: boolean,
  serviceName?: string
): string => {
  const baseNote = isRoundTrip ? 'Round Trip Rate' : 'One Way Rate';
  
  if (calculation.isMultipleVehicles) {
    const vehicleText = calculation.requiredVehicles === 2 ? 'Vehicle' : 'Vehicles';
    return `${calculation.requiredVehicles} ${vehicleText} for ${calculation.totalPassengers} passengers - ${baseNote}`;
  }
  
  return baseNote;
};

/**
 * Calcula el precio total aplicando múltiples vehículos si es necesario
 */
export const calculateServicePrice = (
  basePrice: number,
  calculation: VehicleCalculationResult,
  isAirportShuttle: boolean = false
): number => {
  if (isNaN(basePrice) || basePrice <= 0) {
    return 0;
  }
  
  // Para Airport Shuttle, el precio es por persona
  if (isAirportShuttle) {
    return basePrice * calculation.totalPassengers;
  }
  
  // Para otros servicios, el precio es por vehículo
  return basePrice * calculation.requiredVehicles;
};

/**
 * Verifica si un servicio es Airport Shuttle basado en ID o nombre
 */
export const isAirportShuttleService = (fleetId: string | number, serviceName?: string): boolean => {
  const id = typeof fleetId === 'string' ? fleetId : fleetId.toString();
  return id === '5' || (serviceName && serviceName.toLowerCase().includes('airport shuttle'));
};

/**
 * Función principal que combina todos los cálculos para un servicio
 */
export const calculateServiceDetails = (
  adults: string | number,
  kids: string | number,
  vehicleCapacity: string | number,
  basePrice: number,
  isRoundTrip: boolean,
  fleetId: string | number,
  serviceName?: string
) => {
  const totalPassengers = calculateTotalPassengers(adults, kids);
  const calculation = calculateRequiredVehicles(totalPassengers, vehicleCapacity);
  const isShuttle = isAirportShuttleService(fleetId, serviceName);
  const finalPrice = calculateServicePrice(basePrice, calculation, isShuttle);
  const note = generateServiceNote(calculation, isRoundTrip, serviceName);
  
  return {
    ...calculation,
    finalPrice,
    note,
    isAirportShuttle: isShuttle,
    basePrice
  };
};
