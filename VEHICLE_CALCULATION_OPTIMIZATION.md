# Optimización del Cálculo de Servicios de Transporte

## Resumen de Cambios

Se ha optimizado y escalado la lógica de cálculo de servicios de transporte para manejar dinámicamente el número de vehículos necesarios basado en la capacidad real de cada vehículo y el número total de pasajeros (adultos + niños).

## Problema Anterior

La implementación anterior tenía las siguientes limitaciones:

1. **Lógica hardcodeada**: Solo duplicaba servicios específicos (SUV y Luxury Transportation) cuando había más de 5 pasajeros
2. **No escalable**: No consideraba diferentes capacidades de vehículos
3. **Código duplicado**: La misma lógica estaba repetida en múltiples archivos
4. **Limitado**: Solo manejaba el caso de 2 vehículos máximo

## Solución Implementada

### 1. Utilidades Centralizadas (`src/utils/vehicleCalculations.ts`)

Se creó un archivo de utilidades que centraliza toda la lógica de cálculo:

- `calculateTotalPassengers()`: Calcula el total de pasajeros (adultos + niños)
- `calculateRequiredVehicles()`: Calcula dinámicamente el número de vehículos necesarios
- `generateServiceNote()`: Genera notas descriptivas para los servicios
- `calculateServicePrice()`: Calcula el precio final aplicando múltiples vehículos
- `isAirportShuttleService()`: Identifica servicios de Airport Shuttle
- `calculateServiceDetails()`: Función principal que combina todos los cálculos

### 2. Lógica Escalable

La nueva implementación:

- **Calcula dinámicamente** el número de vehículos: `Math.ceil(totalPassengers / vehicleCapacity)`
- **Maneja cualquier capacidad**: SUV (5), Van (14), o cualquier otra capacidad
- **Escala infinitamente**: Puede calcular 3, 4, 5+ vehículos según sea necesario
- **Diferencia tipos de servicio**: Airport Shuttle (precio por persona) vs otros (precio por vehículo)

### 3. Ejemplos de Funcionamiento

#### SUV Transportation (Capacidad: 5 pasajeros)
- 3 pasajeros → 1 vehículo → Precio base
- 6 pasajeros → 2 vehículos → Precio base × 2
- 12 pasajeros → 3 vehículos → Precio base × 3

#### Van Transportation (Capacidad: 14 pasajeros)
- 10 pasajeros → 1 vehículo → Precio base
- 15 pasajeros → 2 vehículos → Precio base × 2
- 28 pasajeros → 2 vehículos → Precio base × 2

#### Airport Shuttle (Precio por persona)
- 3 pasajeros → Precio base × 3 pasajeros
- 10 pasajeros → Precio base × 10 pasajeros

## Archivos Modificados

### 1. `src/pages/BookingService.tsx`
- Reemplazó `requiresDoubleRate()` con lógica escalable
- Usa utilidades centralizadas para cálculos
- Genera notas dinámicas basadas en número de vehículos

### 2. `src/components/ServiceCardSavingsBanner.tsx`
- Actualizado para usar las nuevas utilidades
- Calcula ahorros considerando múltiples vehículos
- Maneja correctamente Airport Shuttle vs otros servicios

### 3. `src/utils/vehicleCalculations.ts` (Nuevo)
- Centraliza toda la lógica de cálculo
- Funciones puras y testeable
- Documentación completa

### 4. `src/utils/__tests__/vehicleCalculations.test.ts` (Nuevo)
- Suite completa de pruebas unitarias
- Cubre todos los casos de uso y edge cases
- Garantiza la correcta funcionalidad

## Beneficios de la Nueva Implementación

1. **Escalabilidad**: Maneja cualquier número de pasajeros y capacidades de vehículos
2. **Mantenibilidad**: Código centralizado y reutilizable
3. **Flexibilidad**: Fácil agregar nuevos tipos de servicios
4. **Precisión**: Cálculos exactos basados en capacidad real
5. **Testeable**: Funciones puras con pruebas unitarias
6. **Consistencia**: Misma lógica aplicada en toda la aplicación

## Notas Técnicas

- **Compatibilidad**: Mantiene compatibilidad con la API existente
- **Performance**: Cálculos optimizados sin impacto en rendimiento
- **Tipos**: Completamente tipado con TypeScript
- **Error Handling**: Manejo robusto de casos edge y valores inválidos

## Casos de Uso Cubiertos

✅ Servicios con capacidad fija (SUV: 5, Van: 14)
✅ Airport Shuttle con precio por persona
✅ Múltiples vehículos (2, 3, 4+)
✅ Cálculo de ahorros en Round Trip
✅ Notas descriptivas dinámicas
✅ Validación de datos de entrada
✅ Manejo de errores y casos edge

La implementación es ahora completamente escalable y puede manejar cualquier escenario de transporte futuro sin necesidad de modificaciones adicionales.
