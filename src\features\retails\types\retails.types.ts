export interface Retail {
    transaction_id:      string;
    firstName:           string;
    lastName:            string;
    email:               string;
    phone_pax_full:      string;
    subtotal:            number;
    payment_method:      string;
    fleet:               Fleet;
    roundtrip:           boolean;
    adults:              number;
    kids:                number;
    from:                string;
    to:                  string;
    arrivalAirline:      string;
    arrivalFlight:       string;
    arrivalTime:         string;
    arrivalDate:         Date;
    fromdep:             string;
    todep:               string;
    departureAirline:    string;
    departureFlight:     string;
    departureTime:       string;
    departureDate:       Date;
    departurePickup:     string;
    specialInstructions: string;
    carSeats:            number;
    boosterSeats:        number;
    babySeats:           number;
    add_grocery_stop:    boolean;
    grocery_stop:        number;
    add_golf_clubs_bags: boolean;
    golfClubsBags:       number;
    golf_clubs_bags:     number;
    add_surfboards:      boolean;
    surfBoards:          number;
    surfboards:          number;
    authorization_code:  string;
}

export interface Fleet {
    name: string;
}
