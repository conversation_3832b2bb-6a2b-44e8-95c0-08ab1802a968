import { API_ENDPOINTS, ERROR_MESSAGES } from '../../../config/config';
import type { Retail } from '../types/retails.types';

export async function fetchRetails(): Promise<Retail[]> {
  const baseUrl = API_ENDPOINTS.RETAILS.SAVE;
  try {
    const response = await fetch(baseUrl);

    if (!response.ok) {
      throw new Error(ERROR_MESSAGES?.SERVER || 'Error fetching retails');
    }

    const data = await response.json();
    return data as Retail[];
  } catch (error: unknown) {
    // Optionally log error or handle it differently
    if (error instanceof Error) {
      throw new Error(error.message || ERROR_MESSAGES?.UNKNOWN || 'Unknown error');
    }
    throw new Error(ERROR_MESSAGES?.UNKNOWN || 'Unknown error');
  }
}

/**
 * Save booking data to the retails endpoint
 */
export async function saveBooking(bookingData: Retail): Promise<{ success: boolean; transaction_id: string; message?: string }> {
  const baseUrl = API_ENDPOINTS.RETAILS.SAVE;

  try {
    const response = await fetch(baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(bookingData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || ERROR_MESSAGES?.SERVER || 'Error saving booking');
    }

    const data = await response.json();
    return {
      success: true,
      transaction_id: bookingData.transaction_id,
      message: data.message || 'Booking saved successfully'
    };
  } catch (error: unknown) {
    console.error('Error saving booking:', error);
    if (error instanceof Error) {
      throw new Error(error.message || ERROR_MESSAGES?.UNKNOWN || 'Unknown error saving booking');
    }
    throw new Error(ERROR_MESSAGES?.UNKNOWN || 'Unknown error saving booking');
  }
}
