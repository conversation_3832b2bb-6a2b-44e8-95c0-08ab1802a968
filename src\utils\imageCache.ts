/**
 * Utilidad para manejar el cache y precarga de imágenes
 * Optimiza la experiencia del usuario al cargar imágenes de servicios y hoteles
 */

interface ImageCacheEntry {
  url: string;
  loaded: boolean;
  error: boolean;
  timestamp: number;
}

class ImageCache {
  private cache = new Map<string, ImageCacheEntry>();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutos

  /**
   * Precargar una imagen y almacenarla en cache
   */
  async preloadImage(url: string): Promise<boolean> {
    if (!url) return false;

    // Verificar si ya está en cache y es válida
    const cached = this.cache.get(url);
    if (cached && this.isCacheValid(cached)) {
      return cached.loaded && !cached.error;
    }

    return new Promise((resolve) => {
      const img = new Image();
      
      img.onload = () => {
        this.cache.set(url, {
          url,
          loaded: true,
          error: false,
          timestamp: Date.now()
        });
        resolve(true);
      };

      img.onerror = () => {
        this.cache.set(url, {
          url,
          loaded: false,
          error: true,
          timestamp: Date.now()
        });
        resolve(false);
      };

      img.src = url;
    });
  }

  /**
   * Precargar múltiples imágenes en paralelo
   */
  async preloadImages(urls: string[]): Promise<boolean[]> {
    const promises = urls.filter(Boolean).map(url => this.preloadImage(url));
    return Promise.all(promises);
  }

  /**
   * Verificar si una imagen está cargada en cache
   */
  isImageLoaded(url: string): boolean {
    const cached = this.cache.get(url);
    return cached ? cached.loaded && !cached.error && this.isCacheValid(cached) : false;
  }

  /**
   * Verificar si una imagen tuvo error al cargar
   */
  hasImageError(url: string): boolean {
    const cached = this.cache.get(url);
    return cached ? cached.error && this.isCacheValid(cached) : false;
  }

  /**
   * Limpiar cache expirado
   */
  cleanExpiredCache(): void {
    const now = Date.now();
    for (const [url, entry] of this.cache.entries()) {
      if (!this.isCacheValid(entry)) {
        this.cache.delete(url);
      }
    }
  }

  /**
   * Verificar si una entrada de cache es válida
   */
  private isCacheValid(entry: ImageCacheEntry): boolean {
    return Date.now() - entry.timestamp < this.CACHE_DURATION;
  }

  /**
   * Obtener estadísticas del cache
   */
  getCacheStats() {
    const total = this.cache.size;
    const loaded = Array.from(this.cache.values()).filter(entry => entry.loaded && !entry.error).length;
    const errors = Array.from(this.cache.values()).filter(entry => entry.error).length;
    
    return { total, loaded, errors };
  }

  /**
   * Limpiar todo el cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Instancia singleton del cache de imágenes
export const imageCache = new ImageCache();

/**
 * Hook personalizado para usar el cache de imágenes
 */
export const useImageCache = () => {
  return {
    preloadImage: imageCache.preloadImage.bind(imageCache),
    preloadImages: imageCache.preloadImages.bind(imageCache),
    isImageLoaded: imageCache.isImageLoaded.bind(imageCache),
    hasImageError: imageCache.hasImageError.bind(imageCache),
    cleanExpiredCache: imageCache.cleanExpiredCache.bind(imageCache),
    getCacheStats: imageCache.getCacheStats.bind(imageCache),
    clearCache: imageCache.clearCache.bind(imageCache)
  };
};

/**
 * Función utilitaria para obtener la mejor imagen disponible
 */
export const getBestImageUrl = (primaryUrl?: string, fallbackUrl?: string): string => {
  if (primaryUrl && !imageCache.hasImageError(primaryUrl)) {
    return primaryUrl;
  }
  
  if (fallbackUrl && !imageCache.hasImageError(fallbackUrl)) {
    return fallbackUrl;
  }
  
  return primaryUrl || fallbackUrl || '';
};

/**
 * Función para precargar imágenes de servicios
 */
export const preloadServiceImages = async (services: Array<{ imgurl_grande?: string; imgurl?: string }>) => {
  const imageUrls = services
    .map(service => service.imgurl_grande || service.imgurl)
    .filter(Boolean) as string[];
  
  if (imageUrls.length > 0) {
    const results = await imageCache.preloadImages(imageUrls);
    const successCount = results.filter(Boolean).length;
    
    // if (process.env.NODE_ENV === 'development') {
    //   console.log(`🖼️ Preloaded ${successCount}/${imageUrls.length} service images`);
    // }
    
    return successCount;
  }
  
  return 0;
};

/**
 * Función para precargar imágenes de hoteles
 */
export const preloadHotelImages = async (hotels: Array<{ image_url?: string }>) => {
  const imageUrls = hotels
    .map(hotel => hotel.image_url)
    .filter(Boolean) as string[];
  
  if (imageUrls.length > 0) {
    const results = await imageCache.preloadImages(imageUrls);
    const successCount = results.filter(Boolean).length;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🏨 Preloaded ${successCount}/${imageUrls.length} hotel images`);
    }
    
    return successCount;
  }
  
  return 0;
};

// Limpiar cache expirado cada 5 minutos
if (typeof window !== 'undefined') {
  setInterval(() => {
    imageCache.cleanExpiredCache();
  }, 5 * 60 * 1000);
}
