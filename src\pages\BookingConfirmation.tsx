import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Download, Mail, Phone, Calendar, CreditCard, Plane, Car, DollarSign } from 'lucide-react';
import TopBar from '@/components/TopBar';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAnalytics } from '@/shared/hooks';

const BookingConfirmation: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { trackEvent } = useAnalytics();
  const { bookingId, paymentDetails, bookingData, retailData } = location.state || {};

  useEffect(() => {
    // Track successful booking
    trackEvent({
      action: 'booking_completed',
      category: 'Booking',
      label: 'PayPal Payment Success',
      value: bookingData?.totalPrice
    });
  }, [trackEvent, bookingData]);

  // Redirect if no booking data
  if (!bookingId || !paymentDetails || !bookingData) {
    return (
      <div className="min-h-screen bg-gray-50 mt-16">
        <TopBar />
        <Header />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="text-center py-8">
              <p className="text-gray-600 mb-4">No booking information found.</p>
              <Button onClick={() => navigate('/')}>
                Return to Home
              </Button>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return 'Not specified';
    return timeString;
  };

  // Helper function to get contact name
  const getContactName = () => {
    if (bookingData.contactInfo?.firstName && bookingData.contactInfo?.lastName) {
      return `${bookingData.contactInfo.firstName} ${bookingData.contactInfo.lastName}`;
    }
    return bookingData.contactInfo?.name || 'Not specified';
  };

  // Helper function to calculate additional services
  const getAdditionalServices = () => {
    const services = [];
    const additionalItems = bookingData.additionalItems;

    if (additionalItems?.extraServices?.stopShop) {
      services.push({ name: 'Grocery Stop', cost: 25.00 });
    }
    if (additionalItems?.extraServices?.golfBags) {
      services.push({ name: 'Golf Clubs/Bags', cost: 30.00 });
    }
    if (additionalItems?.extraServices?.surfboards) {
      services.push({ name: 'Surfboards', cost: 20.00 });
    }

    return services;
  };

  // Helper function to get payment method label
  const getPaymentMethodLabel = () => {
    const method = paymentDetails.payment_method || bookingData.payment?.method;
    if (method === 'cash') return 'Cash Payment';
    if (method === 'paypal') return 'PayPal';
    return method || 'PayPal';
  };

  // Helper function to get transaction ID label
  const getTransactionIdLabel = () => {
    const method = paymentDetails.payment_method || bookingData.payment?.method;
    if (method === 'cash') return 'Cash ID';
    if (method === 'paypal') return 'PayPal ID';
    return 'Transaction ID';
  };

  // Helper function to check if departure information should be shown
  const shouldShowDepartureInfo = () => {
    // Show if it's explicitly a round trip
    if (bookingData.roundTrip) return true;

    // Show if there's any departure-specific information
    if (bookingData.flightInfo?.departureDate ||
        bookingData.flightInfo?.departureFlightNumber ||
        bookingData.flightInfo?.departureAirline ||
        bookingData.flightInfo?.departureTime) {
      return true;
    }

    // Show if the service type indicates round trip
    if (bookingData.serviceData?.name?.toLowerCase().includes('round trip')) return true;

    return false;
  };

  return (
    <div className="mt-16 min-h-screen bg-gray-50">
      <TopBar />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Success Header */}
          <Card className="mb-8 border-green-200 bg-green-50">
            <CardContent className="text-center py-8">
              <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
              <h1 className="text-3xl font-bold text-green-800 mb-2">
                Booking Confirmed!
              </h1>
              <p className="text-green-700 text-lg mb-4">
                Your transportation has been successfully booked and paid for.
              </p>
              <div className="flex justify-center items-center space-x-4 flex-wrap gap-2">
                <Badge variant="outline" className="bg-white text-green-800 border-green-300">
                  Transaction ID: {bookingId}
                </Badge>
                <Badge variant="outline" className="bg-white text-blue-800 border-blue-300">
                  {getTransactionIdLabel()}: {paymentDetails.id}
                </Badge>
                {retailData && (
                  <Badge variant="outline" className="bg-white text-purple-800 border-purple-300">
                    Fleet: {retailData.fleet.name}
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Booking Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Booking Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Service Information with Vehicle Image */}
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                    <Car className="w-4 h-4 mr-2" />
                    Transportation Service
                  </h3>
                  <div className="flex flex-col sm:flex-row gap-4">
                    {/* Vehicle Image */}
                    {bookingData.serviceData?.image && (
                      <div className="flex-shrink-0">
                        <img
                          src={bookingData.serviceData.image}
                          alt={bookingData.serviceData.name}
                          className="w-32 h-24 object-cover rounded-lg border border-gray-200"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none';
                          }}
                        />
                      </div>
                    )}
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">{bookingData.serviceData?.name || 'Transportation Service'}</p>
                      <p className="text-sm text-gray-600">Vehicle Type: {bookingData.serviceData?.type || 'Standard'}</p>
                      {bookingData.serviceData?.capacity && (
                        <p className="text-sm text-gray-600">Capacity: {bookingData.serviceData.capacity} passengers</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div>
                  <h3 className="font-semibold text-gray-900 mb-2">Contact Information</h3>
                  <div className="space-y-1">
                    <p className="text-gray-600">{getContactName()}</p>
                    <p className="text-gray-600">{bookingData.contactInfo?.email}</p>
                    <p className="text-gray-600">{bookingData.contactInfo?.phone}</p>
                  </div>
                </div>

                {/* Arrival Information */}
                {(bookingData.flightInfo?.flightNumber || bookingData.flightInfo?.airline) && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                      <Plane className="w-4 h-4 mr-2" />
                      Arrival Information
                    </h3>
                    <div className="space-y-1">
                      {bookingData.flightInfo.airline && (
                        <p className="text-gray-600">
                          <span className="font-medium">Airline:</span> {bookingData.flightInfo.airline}
                        </p>
                      )}
                      {bookingData.flightInfo.flightNumber && (
                        <p className="text-gray-600">
                          <span className="font-medium">Flight:</span> {bookingData.flightInfo.flightNumber}
                        </p>
                      )}
                      {bookingData.flightInfo.arrivalDate && (
                        <p className="text-gray-600">
                          <span className="font-medium">Date:</span> {formatDate(bookingData.flightInfo.arrivalDate)}
                        </p>
                      )}
                      {bookingData.flightInfo.arrivalTime && (
                        <p className="text-gray-600">
                          <span className="font-medium">Time:</span> {formatTime(bookingData.flightInfo.arrivalTime)}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* Departure Information (for round trips or when departure info exists) */}
                {shouldShowDepartureInfo() && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                      <Plane className="w-4 h-4 mr-2 transform rotate-45" />
                      Departure Information
                    </h3>
                    <div className="space-y-1">
                      {/* Airline Information */}
                      <p className="text-gray-600">
                        <span className="font-medium">Airline:</span>{' '}
                        {bookingData.flightInfo?.departureAirline ||
                         bookingData.flightInfo?.airline ||
                         <span className="text-amber-600 italic">To be confirmed</span>}
                      </p>

                      {/* Flight Number */}
                      <p className="text-gray-600">
                        <span className="font-medium">Flight:</span>{' '}
                        {bookingData.flightInfo?.departureFlightNumber ||
                         bookingData.flightInfo?.flightNumber ||
                         <span className="text-amber-600 italic">To be confirmed</span>}
                      </p>

                      {/* Departure Date */}
                      <p className="text-gray-600">
                        <span className="font-medium">Date:</span>{' '}
                        {bookingData.flightInfo?.departureDate ?
                          formatDate(bookingData.flightInfo.departureDate) :
                          <span className="text-amber-600 italic">To be confirmed</span>}
                      </p>

                      {/* Departure Time */}
                      <p className="text-gray-600">
                        <span className="font-medium">Time:</span>{' '}
                        {bookingData.flightInfo?.departureTime ?
                          formatTime(bookingData.flightInfo.departureTime) :
                          <span className="text-amber-600 italic">To be confirmed</span>}
                      </p>

                      {/* Note for round trip bookings */}
                      {bookingData.roundTrip && !bookingData.flightInfo?.departureDate && (
                        <div className="mt-2 p-2 bg-blue-50 rounded text-sm text-blue-700">
                          <span className="font-medium">Note:</span> Please provide your departure flight details
                          at least 24 hours before your return date.
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Additional Services */}
                {getAdditionalServices().length > 0 && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2 flex items-center">
                      <DollarSign className="w-4 h-4 mr-2" />
                      Additional Services
                    </h3>
                    <div className="space-y-2">
                      {getAdditionalServices().map((service, index) => (
                        <div key={index} className="flex justify-between items-center bg-gray-50 p-2 rounded">
                          <span className="text-gray-700">{service.name}</span>
                          <span className="font-medium text-gray-900">${service.cost.toFixed(2)} USD</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Special Instructions */}
                {bookingData.additionalItems?.specialInstructions && (
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Special Instructions</h3>
                    <p className="text-gray-600 bg-gray-50 p-3 rounded">{bookingData.additionalItems.specialInstructions}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <CreditCard className="w-5 h-5 mr-2" />
                  Payment Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="font-semibold">Total Amount:</span>
                  <span className="text-xl font-bold text-green-600">
                    ${bookingData.totalPrice} USD
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span>Payment Method:</span>
                  <Badge className={
                    getPaymentMethodLabel() === 'Cash Payment'
                      ? "bg-green-100 text-green-800"
                      : "bg-blue-100 text-blue-800"
                  }>
                    {getPaymentMethodLabel()}
                  </Badge>
                </div>

                <div className="flex justify-between items-center">
                  <span>{getTransactionIdLabel()}:</span>
                  <span className="font-mono text-sm">{paymentDetails.id}</span>
                </div>

                <div className="flex justify-between items-center">
                  <span>Payment Status:</span>
                  <Badge className={
                    paymentDetails.status === 'pending'
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-green-100 text-green-800"
                  }>
                    {paymentDetails.status === 'pending' ? 'Pending' : 'Completed'}
                  </Badge>
                </div>

                {/* Show payer info only for PayPal payments */}
                {paymentDetails.payer?.email_address && (
                  <div className="flex justify-between items-center">
                    <span>Payer:</span>
                    <span>{paymentDetails.payer.email_address}</span>
                  </div>
                )}

                {/* Show contact info for cash payments */}
                {getPaymentMethodLabel() === 'Cash Payment' && (
                  <div className="flex justify-between items-center">
                    <span>Contact:</span>
                    <span>{getContactName()}</span>
                  </div>
                )}

                <div className="flex justify-between items-center">
                  <span>Payment Date:</span>
                  <span>{new Date().toLocaleDateString()}</span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <Card className="mt-8">
            <CardContent className="py-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  onClick={() => window.print()}
                  variant="outline"
                  className="flex items-center"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Print Confirmation
                </Button>
                
                <Button 
                  onClick={() => window.location.href = `mailto:${bookingData.contactInfo?.email}?subject=Booking Confirmation - ${bookingId}`}
                  variant="outline"
                  className="flex items-center"
                >
                  <Mail className="w-4 h-4 mr-2" />
                  Email Confirmation
                </Button>
                
                <Button 
                  onClick={() => window.location.href = 'tel:+526241558009'}
                  variant="outline"
                  className="flex items-center"
                >
                  <Phone className="w-4 h-4 mr-2" />
                  Contact Support
                </Button>
                
                <Button 
                  onClick={() => navigate('/')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Book Another Trip
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Important Information */}
          <Card className="mt-8 border-yellow-200 bg-yellow-50">
            <CardContent className="py-6">
              <h3 className="font-semibold text-yellow-800 mb-3">Important Information</h3>
              <ul className="text-yellow-700 space-y-2 text-sm">
                <li>• Please save this confirmation for your records</li>
                <li>• Our driver will contact you 30 minutes before pickup</li>
                <li>• For any changes or cancellations, please contact us at least 24 hours in advance</li>
                <li>• Keep your booking ID handy: <strong>{bookingId}</strong></li>
                {getPaymentMethodLabel() === 'Cash Payment' && (
                  <li>• <strong>Cash Payment:</strong> Please have the exact amount ready for the driver</li>
                )}
                {getPaymentMethodLabel() === 'PayPal' && (
                  <li>• <strong>PayPal Payment:</strong> Your payment has been processed successfully</li>
                )}
                <li>• For support, call +52 ************ <NAME_EMAIL></li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default BookingConfirmation;
