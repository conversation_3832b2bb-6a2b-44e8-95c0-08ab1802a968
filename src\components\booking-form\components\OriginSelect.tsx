import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

interface OriginSelectProps {
  value: string;
  onChange: (value: string) => void;
  error?: boolean;
  className?: string;
  airportOptions: Array<{
    value?: string | number;
    label?: string;
    city?: string;
    code?: string;
  }>;
  loading?: boolean;
  errorText?: string;
  required?: boolean;
  placeholder?: string;
}

export const OriginSelect = ({
  value,
  onChange,
  error,
  className,
  airportOptions,
  loading,
  errorText,
  placeholder = "Select pickup location",
  required
}: OriginSelectProps) => {
  const currentPlaceholder = loading ? "Loading airports..." : placeholder;

  return (
    <div className="w-full">      
      <Select 
        value={value || ""} 
        onValueChange={onChange} 
        required={required}
      >
        <SelectTrigger 
          className={cn(
            "h-12",
            "relative",
            error && "border-red-500",
            !value && "text-muted-foreground",
            className
          )}
        >
          {value ? <SelectValue /> : <span className="text-muted-foreground">{currentPlaceholder}</span>}
        </SelectTrigger>
        <SelectContent 
          className="bg-white z-50 max-h-[300px] overflow-y-auto"
          position="popper"
          sideOffset={8}
        >
          {loading ? (
            <SelectItem value="loading" disabled>Loading airports...</SelectItem>
          ) : (
            airportOptions.map((airport, index) => (
              <SelectItem
                key={`airport-${airport.value || airport.label}-${index}`}
                value={airport.value?.toString() || ''}
                className="py-3 px-4 text-base cursor-pointer hover:bg-gray-50"
              >
                {airport.label} - {airport.city}
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      {/* Reserve space for error message to prevent layout shift */}
      <div className="min-h-[20px] mt-1">
        {error && <p className="text-red-500 text-xs">{errorText || "Required"}</p>}
      </div>
    </div>
  );
};
