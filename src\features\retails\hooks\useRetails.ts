import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Retail } from '../types/retails.types';
import { fetchRetails, saveBooking } from '../services/retails.service';

export function useRetails() {
  return useQuery<Retail[]>({
    queryKey: ['retails'],
    queryFn: fetchRetails,
  });
}

/**
 * Hook for saving booking data
 */
export function useSaveBooking() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: saveBooking,
    onSuccess: () => {
      // Invalidate and refetch retails data
      queryClient.invalidateQueries({ queryKey: ['retails'] });
    },
    onError: (error) => {
      console.error('Error saving booking:', error);
    },
  });
}