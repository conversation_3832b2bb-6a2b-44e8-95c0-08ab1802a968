/**
 * Application Configuration
 * Centralized configuration for BajaTravel Shuttle React App
 */

// Safe environment access for browser compatibility
const getEnvVar = (key: string): string | undefined => {
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key];
  }
  // Fallback for Vite - usar import.meta.env directamente
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[key];
  }
  return undefined;
};

// Environment detection - usando NODE_ENV con respaldo a REACT_APP_ENVIRONMENT
const getEnvironment = (): string => {
  // NODE_ENV es establecido automáticamente por React scripts
  const nodeEnv = getEnvVar('NODE_ENV');
  if (nodeEnv) {
    return nodeEnv;
  }
  
  // Respaldo a nuestra variable personalizada
  const reactAppEnv = getEnvVar('VITE_ENVIRONMENT');
  if (reactAppEnv) {
    return reactAppEnv;
  }
  
  // Respaldo final
  return reactAppEnv || 'development';
};

const currentEnvironment = getEnvironment();

// Environment booleans
export const isDevelopment = getEnvironment() === 'development';
export const isProduction = currentEnvironment === 'production';
export const isTesting = currentEnvironment === 'test';
// Compute API base URL. In development we route through Vite proxy using same origin ('').
const ENV_BASE_URL = '';
const COMPUTED_BASE_URL = isDevelopment ?  getEnvVar('VITE_API_BASE_URL_LOCAL') : getEnvVar('VITE_API_BASE_URL_REMOTE');

// API Configuration
export const API_CONFIG = {
  BASE_URL: COMPUTED_BASE_URL,
  API_PREFIX: '/api',
  VERSION: getEnvVar('VITE_API_VERSION') || 'v1',
  TIMEOUT: parseInt(getEnvVar('VITE_API_TIMEOUT') || '10000'),
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  
  // URL base completa para la API versionada
  get VERSIONED_BASE_URL() {
    const apiEndpoint = `${this.BASE_URL}${this.API_PREFIX}/${this.VERSION}`;
    // console.log(isDevelopment, apiEndpoint);
    return apiEndpoint;
  },
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  // Base endpoints
  TEST: `${API_CONFIG.VERSIONED_BASE_URL}/test`,
  HEALTH: `${API_CONFIG.VERSIONED_BASE_URL}/health`,
  STATS: `${API_CONFIG.VERSIONED_BASE_URL}/stats`,
  
  // Airlines endpoints
  AIRLINES: {
    ALL: `${API_CONFIG.VERSIONED_BASE_URL}/airlines`,
  },
  
  // Airports endpoints
  AIRPORTS: {
    ALL: `${API_CONFIG.VERSIONED_BASE_URL}/airports`,
  },

  // Zones endpoints
  ZONES: {
    ALL: `${API_CONFIG.VERSIONED_BASE_URL}/zones`,
    BY_ID: (id: number) => `${API_CONFIG.VERSIONED_BASE_URL}/zones/${id}`,
    POPULAR: (limit: number = 5) => `${API_CONFIG.VERSIONED_BASE_URL}/zones/popular/${limit}`,
  },
  
  // Hotels endpoints
  HOTELS: {
    ALL: `${API_CONFIG.VERSIONED_BASE_URL}/hotels`,
    BY_ZONE: (zoneId: number) => `${API_CONFIG.VERSIONED_BASE_URL}/hotels/zone/${zoneId}`,
  },
  
  // Rates endpoints
  RATES: {
    ALL: `${API_CONFIG.VERSIONED_BASE_URL}/rates`,
    DETAILED: `${API_CONFIG.VERSIONED_BASE_URL}/rates/detailed`,
    BY_ZONE: (zoneId: number) => `${API_CONFIG.VERSIONED_BASE_URL}/rates/zone/${zoneId}`,
    BY_ZONE_GROUPED: (zoneId: number) => `${API_CONFIG.VERSIONED_BASE_URL}/rates/grouped/zone/${zoneId}`,
    BY_FLEET: (fleetId: number) => `${API_CONFIG.VERSIONED_BASE_URL}/rates/fleet/${fleetId}`,
  },
  
  // Fleet endpoints
  FLEETS: {
    ALL: `${API_CONFIG.VERSIONED_BASE_URL}/fleets`,
    DETAILED: `${API_CONFIG.VERSIONED_BASE_URL}/fleets/detailed`,
    BY_ZONE: (zoneId: number) => `${API_CONFIG.VERSIONED_BASE_URL}/fleets/zone/${zoneId}`,
  },

  // Retails endpoints
  RETAILS: {
    SAVE: `${API_CONFIG.VERSIONED_BASE_URL}/save-trans-booking`,
  },
  
  // Shared rates
  SHARED_RATES: `${API_CONFIG.VERSIONED_BASE_URL}/shared-rates`,
} as const;

// Application Configuration
export const APP_CONFIG = {
  // App information
  NAME: getEnvVar('VITE_NAME') || 'BajaTravel Shuttle',
  VERSION: getEnvVar('VITE_VERSION') || '2.0.0',
  DESCRIPTION: 'Transportation booking system for Baja California',
  
  // Environment info - usando nuestra función helper
  ENVIRONMENT: currentEnvironment,
  
  // Company information
  COMPANY: {
    NAME: getEnvVar('VITE_COMPANY_NAME') || 'BajaTravel',
    PHONE: getEnvVar('VITE_COMPANY_PHONE') || '+52 624 XXX XXXX',
    EMAIL: getEnvVar('VITE_COMPANY_EMAIL') || '<EMAIL>',
    ADDRESS: 'Los Cabos, Baja California Sur, México',
  },
  
  // Default values
  DEFAULTS: {
    CURRENCY: getEnvVar('VITE_DEFAULT_CURRENCY') || 'USD',
    LANGUAGE: getEnvVar('VITE_DEFAULT_LANGUAGE') || 'en',
    TIMEZONE: 'America/Mazatlan',
    ITEMS_PER_PAGE: 10,
    DEFAULT_ZONE_ID: parseInt(getEnvVar('VITE_DEFAULT_ZONE_ID') || '1'),
  },
  
  // Feature flags - usando la lógica de environment correcta
  FEATURES: {
    ENABLE_BOOKING: getEnvVar('VITE_ENABLE_BOOKING') === 'true',
    ENABLE_PAYMENTS: getEnvVar('VITE_ENABLE_PAYMENTS') === 'true',
    ENABLE_NOTIFICATIONS: true,
    ENABLE_ANALYTICS: getEnvVar('VITE_ENABLE_ANALYTICS') === 'true' || isProduction,
    ENABLE_DEBUG: getEnvVar('VITE_DEBUG') === 'true' || isDevelopment,
  },
} as const;

// UI Configuration
export const UI_CONFIG = {
  THEME: {
    PRIMARY_COLOR: '#2563eb',
    SECONDARY_COLOR: '#f59e0b',
    SUCCESS_COLOR: '#10b981',
    ERROR_COLOR: '#ef4444',
    WARNING_COLOR: '#f59e0b',
  },
  
  LAYOUT: {
    HEADER_HEIGHT: '64px',
    SIDEBAR_WIDTH: '280px',
    FOOTER_HEIGHT: '60px',
    CONTAINER_MAX_WIDTH: '1200px',
  },
  
  ANIMATIONS: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  
  BREAKPOINTS: {
    SM: '640px',
    MD: '768px',
    LG: '1024px',
    XL: '1280px',
    '2XL': '1536px',
  },
} as const;

// Cache Configuration
export const CACHE_CONFIG = {
  KEYS: {
    ZONES: 'bajatravel_zones',
    HOTELS: 'bajatravel_hotels',
    RATES: 'bajatravel_rates',
    FLEETS: 'bajatravel_fleets',
    AIRPORTS: 'bajatravel_airports',
    USER_PREFERENCES: 'bajatravel_user_prefs',
  },
  
  DEFAULT_DURATION: parseInt(getEnvVar('VITE_CACHE_DURATION') || '300000'),
  
  EXPIRATION: {
    SHORT: 5 * 60 * 1000,
    MEDIUM: 30 * 60 * 1000,
    LONG: 2 * 60 * 60 * 1000,
    VERY_LONG: 24 * 60 * 60 * 1000,
  },
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK: 'Network error. Please check your connection.',
  TIMEOUT: 'Request timeout. Please try again.',
  SERVER: 'Server error. Please try again later.',
  NOT_FOUND: 'Resource not found.',
  VALIDATION: 'Please check your input and try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  UNKNOWN: 'An unexpected error occurred.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  BOOKING_CREATED: 'Booking created successfully!',
  BOOKING_UPDATED: 'Booking updated successfully!',
  BOOKING_CANCELLED: 'Booking cancelled successfully!',
  DATA_SAVED: 'Data saved successfully!',
  EMAIL_SENT: 'Email sent successfully!',
} as const;

// Validation Rules
export const VALIDATION = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[\d\s\-()]+$/,
  MIN_PASSWORD_LENGTH: 8,
  MAX_NAME_LENGTH: 50,
  MAX_MESSAGE_LENGTH: 500,
} as const;

// Development/Debug Configuration
export const DEBUG_CONFIG = {
  ENABLE_CONSOLE_LOGS: APP_CONFIG.FEATURES.ENABLE_DEBUG,
  ENABLE_REDUX_DEVTOOLS: isDevelopment,
  ENABLE_REACT_QUERY_DEVTOOLS: isDevelopment,
  LOG_LEVEL: isDevelopment ? 'debug' : 'error',
  SHOW_ENVIRONMENT_INFO: isDevelopment,
} as const;

// Helper functions
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${API_CONFIG.API_PREFIX}${endpoint}`;
};

export const isFeatureEnabled = (feature: keyof typeof APP_CONFIG.FEATURES): boolean => {
  return APP_CONFIG.FEATURES[feature];
};

export const getCacheKey = (key: keyof typeof CACHE_CONFIG.KEYS, suffix?: string): string => {
  return suffix ? `${CACHE_CONFIG.KEYS[key]}_${suffix}` : CACHE_CONFIG.KEYS[key];
};

// Environment info helper - ahora muestra ambas variables
export const getEnvironmentInfo = () => {
  return {
    environment: currentEnvironment,
    nodeEnv: getEnvVar('NODE_ENV') || 'not set',
    reactAppEnv: getEnvVar('VITE_ENVIRONMENT') || 'not set',
    isDevelopment,
    isProduction,
    isTesting,
  };
};

// Debug helper para verificar variables
export const debugEnvironmentVariables = () => {
  if (isDevelopment) {
    console.group('🔧 Environment Variables Debug');
    console.log('NODE_ENV:', getEnvVar('NODE_ENV'));
    console.log('VITE_ENVIRONMENT:', getEnvVar('VITE_ENVIRONMENT'));
    console.log('Current Environment:', currentEnvironment);
    console.log('Environment Info:', getEnvironmentInfo());
    console.groupEnd();
  }
};