// Export all components for easy importing
export { default as BookingDetailsHeader } from './BookingDetailsHeader';
export { default as BackNavigation } from './BackNavigation';
export { default as TransferBookingCard } from './TransferBookingCard';
export { default as ContactInformationCard } from './ContactInformationCard';
export { default as FlightInformationCard } from './FlightInformationCard';
export { default as AdditionalItemsCard } from './AdditionalItemsCard';
export { default as CheckoutButton } from './CheckoutButton';
export { default as RoundTripSavingsBanner } from './RoundTripSavingsBanner';

// Export types from schemas
export type {
  ContactInfoFormData,
  FlightInfoFormData,
  AdditionalItemsFormData,
  BookingDetailsFormData
} from '../schemas/bookingDetailsSchema';
