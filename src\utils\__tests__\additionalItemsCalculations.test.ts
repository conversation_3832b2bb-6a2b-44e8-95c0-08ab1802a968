/**
 * Pruebas unitarias para las utilidades de cálculo de servicios adicionales
 */

import {
  calculateExtraServiceCost,
  calculateChildSeatCost,
  calculateAdditionalItemsCosts,
  calculateTotalTransportationPrice,
  formatPrice,
  generateAdditionalItemsSummary,
  EXTRA_SERVICE_PRICES,
  CHILD_SEAT_PRICES
} from '../additionalItemsCalculations';

import type { AdditionalItemsFormData } from '@/pages/BookingDetails/schemas/bookingDetailsSchema';

describe('additionalItemsCalculations', () => {
  describe('calculateExtraServiceCost', () => {
    it('should calculate stop shop cost correctly when selected', () => {
      const result = calculateExtraServiceCost('stopShop', true);
      
      expect(result).toEqual({
        service: 'stopShop',
        quantity: 1,
        unitPrice: 30.00,
        totalPrice: 30.00,
        description: 'Stop and Shop: Grocery Store, Supermarket (30 min)'
      });
    });

    it('should return zero cost when service is not selected', () => {
      const result = calculateExtraServiceCost('golfBags', false);
      
      expect(result).toEqual({
        service: 'golfBags',
        quantity: 0,
        unitPrice: 20.00,
        totalPrice: 0,
        description: 'Golf clubs bags'
      });
    });

    it('should calculate multiple quantity correctly', () => {
      const result = calculateExtraServiceCost('surfboards', true, 3);
      
      expect(result).toEqual({
        service: 'surfboards',
        quantity: 3,
        unitPrice: 20.00,
        totalPrice: 60.00,
        description: 'Surfboards'
      });
    });
  });

  describe('calculateChildSeatCost', () => {
    it('should calculate baby seat cost (always free)', () => {
      const result = calculateChildSeatCost('babySeat', 2);
      
      expect(result).toEqual({
        service: 'babySeat',
        quantity: 2,
        unitPrice: 0,
        totalPrice: 0,
        description: 'Baby Seat (0-1 years)'
      });
    });

    it('should handle string quantity input', () => {
      const result = calculateChildSeatCost('carSeat', '3');
      
      expect(result).toEqual({
        service: 'carSeat',
        quantity: 3,
        unitPrice: 0,
        totalPrice: 0,
        description: 'Car Seat (2-4 years)'
      });
    });

    it('should handle invalid string quantity', () => {
      const result = calculateChildSeatCost('boosterSeat', 'invalid');
      
      expect(result).toEqual({
        service: 'boosterSeat',
        quantity: 0,
        unitPrice: 0,
        totalPrice: 0,
        description: 'Booster Seat (5-8 years)'
      });
    });
  });

  describe('calculateAdditionalItemsCosts', () => {
    it('should return empty breakdown when no additional items', () => {
      const result = calculateAdditionalItemsCosts();
      
      expect(result).toEqual({
        extraServices: [],
        childSeats: [],
        totalExtraServicesCost: 0,
        totalChildSeatsCost: 0,
        grandTotal: 0
      });
    });

    it('should calculate complete breakdown with all services', () => {
      const additionalItems: AdditionalItemsFormData = {
        babySeat: '1',
        carSeat: '2',
        boosterSeat: '0',
        specialInstructions: 'Test instructions',
        extraServices: {
          stopShop: true,
          golfBags: true,
          surfboards: false
        }
      };

      const result = calculateAdditionalItemsCosts(additionalItems);
      
      expect(result.extraServices).toHaveLength(2); // stopShop + golfBags
      expect(result.childSeats).toHaveLength(2); // babySeat + carSeat (boosterSeat is 0)
      expect(result.totalExtraServicesCost).toBe(50.00); // 30 + 20
      expect(result.totalChildSeatsCost).toBe(0); // All child seats are free
      expect(result.grandTotal).toBe(50.00);
    });

    it('should filter out unselected services', () => {
      const additionalItems: AdditionalItemsFormData = {
        babySeat: '0',
        carSeat: '0',
        boosterSeat: '0',
        specialInstructions: '',
        extraServices: {
          stopShop: false,
          golfBags: false,
          surfboards: false
        }
      };

      const result = calculateAdditionalItemsCosts(additionalItems);
      
      expect(result.extraServices).toHaveLength(0);
      expect(result.childSeats).toHaveLength(0);
      expect(result.grandTotal).toBe(0);
    });
  });

  describe('calculateTotalTransportationPrice', () => {
    it('should calculate total price with additional items', () => {
      const basePrice = 100;
      const additionalItems: AdditionalItemsFormData = {
        babySeat: '1',
        carSeat: '0',
        boosterSeat: '0',
        specialInstructions: '',
        extraServices: {
          stopShop: true,
          golfBags: false,
          surfboards: true
        }
      };

      const result = calculateTotalTransportationPrice(basePrice, additionalItems);
      
      expect(result.basePrice).toBe(100);
      expect(result.additionalItemsCost).toBe(50); // 30 + 20
      expect(result.totalPrice).toBe(150);
      expect(result.breakdown.grandTotal).toBe(50);
    });

    it('should handle no additional items', () => {
      const basePrice = 75;
      
      const result = calculateTotalTransportationPrice(basePrice);
      
      expect(result.basePrice).toBe(75);
      expect(result.additionalItemsCost).toBe(0);
      expect(result.totalPrice).toBe(75);
    });
  });

  describe('formatPrice', () => {
    it('should format price correctly', () => {
      expect(formatPrice(30)).toBe('$30.00');
      expect(formatPrice(30.5)).toBe('$30.50');
      expect(formatPrice(30.99)).toBe('$30.99');
    });
  });

  describe('generateAdditionalItemsSummary', () => {
    it('should generate summary for selected items', () => {
      const additionalItems: AdditionalItemsFormData = {
        babySeat: '2',
        carSeat: '1',
        boosterSeat: '0',
        specialInstructions: '',
        extraServices: {
          stopShop: true,
          golfBags: false,
          surfboards: true
        }
      };

      const result = generateAdditionalItemsSummary(additionalItems);
      
      expect(result).toContain('Stop and Shop: Grocery Store, Supermarket (30 min): $30.00');
      expect(result).toContain('Surfboards: $20.00');
      expect(result).toContain('Baby Seat (0-1 years): 2 seats (Free)');
      expect(result).toContain('Car Seat (2-4 years): 1 seat (Free)');
      expect(result).not.toContain('Golf clubs bags');
      expect(result).not.toContain('Booster Seat');
    });

    it('should return empty array when no items selected', () => {
      const result = generateAdditionalItemsSummary();
      
      expect(result).toEqual([]);
    });
  });

  describe('Constants', () => {
    it('should have correct extra service prices', () => {
      expect(EXTRA_SERVICE_PRICES.stopShop).toBe(30.00);
      expect(EXTRA_SERVICE_PRICES.golfBags).toBe(20.00);
      expect(EXTRA_SERVICE_PRICES.surfboards).toBe(20.00);
    });

    it('should have correct child seat prices (all free)', () => {
      expect(CHILD_SEAT_PRICES.babySeat).toBe(0);
      expect(CHILD_SEAT_PRICES.carSeat).toBe(0);
      expect(CHILD_SEAT_PRICES.boosterSeat).toBe(0);
    });
  });
});
