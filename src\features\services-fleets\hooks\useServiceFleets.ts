import { useQuery } from '@tanstack/react-query';
import type { ServiceFleets } from '../types/service-fleets.types';
import { fetchDetailedFleets } from '../services/services-fleets.service';

export function useServiceFleets() {
    const {
        data,
        isLoading: loading,
        error
    } = useQuery<ServiceFleets[], Error>({
        queryKey: ['serviceFleets'],
        queryFn: fetchDetailedFleets
    });
    // console.log({data})
    return {
        data: data ?? null,
        loading,
        error: error ? error.message : null
    };
}