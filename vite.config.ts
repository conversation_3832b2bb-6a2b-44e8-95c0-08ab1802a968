import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    // Configuración CORS mejorada para PayPal
    cors: {
      origin: [
        'http://localhost:8080',
        'http://localhost:8081',
        'http://127.0.0.1:8080',
        'http://127.0.0.1:8081',
        'https://www.sandbox.paypal.com',
        'https://www.paypal.com',
        'https://js.paypal.com'
      ],
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    },
    proxy: {
      // Proxiamos la API para evitar CORS en desarrollo
      '/api': {
        target: 'http://localhost/bajatravel',
        changeOrigin: true,
        secure: false,
        // Reescribe /api -> /api para mantener prefijo
        // Si tu backend espera /bajatravel/api, este target ya lo incluye
        // por lo que no necesitamos rewrite aquí.
        // En caso de mover, podrías usar:
        // rewrite: (path) => path.replace(/^\/api/, '/api')
      },
    },
    // Headers adicionales para mejorar compatibilidad con PayPal
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
    }
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
