
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from 'react-helmet-async';
import Index from "./pages/Index";
import DestinationDetail from "./pages/DestinationDetail";
import BookingService from "./pages/BookingService";
import BookingDetails from "./pages/BookingDetails";
import BookingConfirmation from "./pages/BookingConfirmation";
import TestHotelSearch from "./pages/TestHotelSearch";
import TestAirports from "./pages/TestAirports";
import HotelSearchComparison from "./pages/HotelSearchComparison";
import NotFound from "./pages/NotFound";
import { useEffect } from 'react';

const queryClient = new QueryClient();

const App = () => {
  useEffect(() => {
    // Google Tag Manager
    const gtmScript = document.createElement('script');
    gtmScript.innerHTML = `
      (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
      new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
      j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
      'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-XXXXXXX');
    `;
    document.head.appendChild(gtmScript);

    // Initialize dataLayer
    window.dataLayer = window.dataLayer || [];
  }, []);

  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            {/* Google Tag Manager (noscript) */}
            <noscript>
              <iframe 
                src="https://www.googletagmanager.com/ns.html?id=GTM-XXXXXXX"
                height="0" 
                width="0" 
                style={{ display: 'none', visibility: 'hidden' }}
              />
            </noscript>
            <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/destinations/:slug" element={<DestinationDetail />} />
            <Route path="/booking-service" element={<BookingService />} />
            <Route path="/booking-details" element={<BookingDetails />} />
            <Route path="/booking-confirmation" element={<BookingConfirmation />} />
            <Route path="/test-hotel-search" element={<TestHotelSearch />} />
            <Route path="/test-airports" element={<TestAirports />} />
            <Route path="/hotel-search-comparison" element={<HotelSearchComparison />} />
            <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </HelmetProvider>
  );
};

export default App;
