#!/usr/bin/env node

/**
 * Script para deploy rápido a servidor de pruebas
 * Uso: node deploy-test.js [servidor]
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuraciones de servidores de prueba
const servers = {
  netlify: {
    name: 'Netlify',
    command: 'npx netlify deploy --prod --dir=dist',
    setup: 'npm install -g netlify-cli && netlify login',
    description: 'Deploy gratuito a Netlify (recomendado)'
  },
  vercel: {
    name: 'Vercel',
    command: 'npx vercel --prod',
    setup: 'npm install -g vercel && vercel login',
    description: 'Deploy gratuito a Vercel'
  },
  surge: {
    name: 'Surge.sh',
    command: 'npx surge dist',
    setup: 'npm install -g surge',
    description: 'Deploy rápido a Surge.sh'
  }
};

function showHelp() {
  console.log('\n🚀 Deploy Test Script para BajaTravel\n');
  console.log('Uso: node deploy-test.js [servidor]\n');
  console.log('Servidores disponibles:');
  
  Object.entries(servers).forEach(([key, server]) => {
    console.log(`  ${key.padEnd(10)} - ${server.description}`);
  });
  
  console.log('\nEjemplos:');
  console.log('  node deploy-test.js netlify');
  console.log('  node deploy-test.js vercel');
  console.log('  node deploy-test.js surge\n');
}

function createProductionEnv() {
  console.log('📝 Creando .env.production para deploy...');
  
  const prodEnv = `# Production Environment for Testing
VITE_API_BASE_URL_LOCAL=https://api-web-btt.transroute.com.mx
VITE_API_BASE_URL_REMOTE=https://api-web-btt.transroute.com.mx
VITE_ENVIRONMENT=production

# PayPal Configuration for Testing
VITE_PAYPAL_CLIENT_ID=Aa08OAhne3Qy90N8vSIB4_qaPc5sMU49k2zCYoLuwjAu4WHIe2SBNE1-2u-yYMrrEA3_63nwNyZC6BO9
VITE_PAYPAL_ENVIRONMENT=sandbox
VITE_PAYPAL_SIMULATE_IN_DEV=false
VITE_PAYPAL_FORCE_SIMULATION=false

# Other settings
VITE_ENABLE_BOOKING=true
VITE_ENABLE_PAYMENTS=true
VITE_ENABLE_ANALYTICS=true
`;

  fs.writeFileSync('.env.production', prodEnv);
  console.log('✅ .env.production creado');
}

function buildProject() {
  console.log('🔨 Construyendo proyecto...');
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Build completado');
  } catch (error) {
    console.error('❌ Error en build:', error.message);
    process.exit(1);
  }
}

function deployToServer(serverKey) {
  const server = servers[serverKey];
  if (!server) {
    console.error(`❌ Servidor "${serverKey}" no encontrado`);
    showHelp();
    process.exit(1);
  }

  console.log(`🚀 Desplegando a ${server.name}...`);
  
  try {
    execSync(server.command, { stdio: 'inherit' });
    console.log(`✅ Deploy a ${server.name} completado!`);
    console.log('\n🎉 Tu aplicación está lista para probar PayPal!');
    console.log('💡 Ahora puedes probar los pagos sin problemas de CORS');
  } catch (error) {
    console.error(`❌ Error en deploy a ${server.name}:`, error.message);
    console.log(`\n💡 Primero ejecuta: ${server.setup}`);
    process.exit(1);
  }
}

function main() {
  const serverKey = process.argv[2];
  
  if (!serverKey || serverKey === '--help' || serverKey === '-h') {
    showHelp();
    return;
  }

  console.log('🚀 Iniciando deploy de prueba para BajaTravel...\n');
  
  // Crear env de producción
  createProductionEnv();
  
  // Build del proyecto
  buildProject();
  
  // Deploy al servidor
  deployToServer(serverKey);
}

if (require.main === module) {
  main();
}
