import { Card, CardContent } from '@/components/ui/card';
import { CheckCircleIcon } from 'lucide-react';
import { useBooking } from '@/context/BookingContext';
import { useServiceFleetsRates } from '@/features/services-fleets/hooks/userServiceFleetRates';
import type { ServiceFleetRates } from '@/features/services-fleets/types/service-fleet-rates.types';

interface RoundTripSavingsBannerProps {
  serviceData?: {
    id: number;
    price: number;
  };
}

const RoundTripSavingsBanner = ({ serviceData }: RoundTripSavingsBannerProps) => {
  const { state } = useBooking();

  // Usar el servicio del contexto si está disponible, sino usar el prop
  const currentService = state.selectedService || serviceData;
  
  // Obtener el zone_id del hotel seleccionado
  const zoneId = state.selectedHotel?.zone_id?.toString();
  
  // Obtener las tarifas para la zona
  const { serviceFleetsRates: rates, loading: isLoading } = useServiceFleetsRates(zoneId || '');

  // Calcular el descuento solo si tenemos datos y es un viaje round trip
  const calculateSavings = () => {
    if (!rates || !currentService || !state.roundTrip || isLoading) {
      return null;
    }

    // Buscar la tarifa específica para este servicio
    const serviceRate = rates.find((rate: ServiceFleetRates) =>
      rate.fleet_id === currentService.id.toString()
    );

    if (!serviceRate) {
      return null;
    }

    const oneWayPrice = Number(serviceRate.one_way.price);
    const roundTripPrice = Number(serviceRate.round_trip.price);
    
    // Calcular el precio de dos viajes one way
    const twoOneWayPrice = oneWayPrice * 2;
    
    // Calcular el ahorro
    const savings = twoOneWayPrice - roundTripPrice;
    const savingsPercentage = Math.round((savings / twoOneWayPrice) * 100);

    // Solo mostrar si hay ahorro significativo (más de $5 o 5%)
    if (savings > 5 && savingsPercentage > 0) {
      return {
        oneWayPrice,
        twoOneWayPrice,
        roundTripPrice,
        savings,
        savingsPercentage
      };
    }

    return null;
  };

  const savingsData = calculateSavings();

  // No mostrar el banner si no hay datos de ahorro o no es round trip
  if (!savingsData || !state.roundTrip) {
    return null;
  }

  return (
    <Card className="border-2 border-green-200 bg-gradient-to-r from-green-50 to-emerald-50 shadow-md">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            <CheckCircleIcon className="w-6 h-6 text-green-600 mt-0.5" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-green-700 font-semibold text-sm">
                ✓ Best Price
              </span>
            </div>
            
            <div className="space-y-1">
              <div className="text-sm text-gray-700">
                <span className="text-gray-500">One way price:</span>
                <span className="ml-2 line-through text-gray-500">
                  ${savingsData.twoOneWayPrice.toFixed(2)}
                </span>
              </div>
              
              <div className="text-sm font-medium text-green-700">
                <span className="text-green-600">
                  Save ${savingsData.savings.toFixed(2)} ({savingsData.savingsPercentage}% off)
                </span>
                <span className="text-gray-600 ml-1">
                  vs. booking two One-Way trips
                </span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RoundTripSavingsBanner;
