# PayPal Development Guide

## Configuración para Pruebas Locales

### Problema Común: Errores de CORS en Localhost

Cuando desarrollas en localhost, es común encontrar errores de CORS con PayPal como:
- `Failed to load resource: net::ERR_CONNECTION_REFUSED`
- `Access to XMLHttpRequest blocked by CORS policy`
- `Refused to evaluate a string as JavaScript because 'unsafe-eval'`

### Soluciones Implementadas

#### 1. Configuración Automática de Entorno

El componente `PayPalButton` detecta automáticamente si estás en desarrollo local y aplica configuraciones optimizadas:

```typescript
// Detecta localhost automáticamente
const isLocalDevelopment = window.location.hostname === 'localhost' || 
                          window.location.hostname === '127.0.0.1';
```

#### 2. Variables de Entorno para Control

En tu archivo `.env`, puedes controlar el comportamiento de PayPal:

```env
# PayPal Configuration
VITE_PAYPAL_CLIENT_ID=tu_client_id_sandbox
VITE_PAYPAL_ENVIRONMENT=sandbox

# Opciones de desarrollo
VITE_PAYPAL_SIMULATE_IN_DEV=false    # Habilita simulación automática en localhost
VITE_PAYPAL_FORCE_SIMULATION=false   # Fuerza simulación siempre
```

#### 3. Modos de Operación

##### Modo Normal (Recomendado)
```env
VITE_PAYPAL_SIMULATE_IN_DEV=false
VITE_PAYPAL_FORCE_SIMULATION=false
```
- Usa PayPal sandbox real
- Mejor para pruebas completas

##### Modo Simulación Automática
```env
VITE_PAYPAL_SIMULATE_IN_DEV=true
VITE_PAYPAL_FORCE_SIMULATION=false
```
- Simula PayPal solo en localhost cuando hay problemas de CORS
- Fallback automático

##### Modo Simulación Forzada
```env
VITE_PAYPAL_SIMULATE_IN_DEV=false
VITE_PAYPAL_FORCE_SIMULATION=true
```
- Siempre simula PayPal (útil sin internet)
- Para desarrollo offline

### Cómo Usar

#### Para Pruebas Normales:
1. Mantén las variables en `false`
2. Usa tu Client ID de sandbox de PayPal
3. Prueba con cuentas de sandbox de PayPal

#### Si Tienes Problemas de CORS:
1. Cambia `VITE_PAYPAL_SIMULATE_IN_DEV=true`
2. Reinicia el servidor de desarrollo
3. El botón mostrará "Simulate PayPal Payment"

#### Para Desarrollo Offline:
1. Cambia `VITE_PAYPAL_FORCE_SIMULATION=true`
2. Reinicia el servidor
3. Siempre usará simulación

### Datos de Prueba Simulados

Cuando usas simulación, se generan datos mock realistas:

```json
{
  "id": "MOCK_ORDER_1234567890",
  "status": "COMPLETED",
  "payer": {
    "name": { "given_name": "Test", "surname": "User" },
    "email_address": "<EMAIL>"
  }
}
```

### Cuentas de Sandbox PayPal

Para pruebas reales con sandbox, usa estas cuentas de prueba:

**Comprador:**
- Email: <EMAIL>
- Password: test1234

**Vendedor:**
- Email: <EMAIL>
- Password: test1234

### Troubleshooting

#### Error: "PayPal script failed to load"
- Verifica tu Client ID
- Asegúrate de estar en modo sandbox
- Habilita simulación temporalmente

#### Error: "CORS policy blocked"
- Habilita `VITE_PAYPAL_SIMULATE_IN_DEV=true`
- Verifica la configuración de Vite
- Usa simulación como fallback

#### Error: "Invalid client ID"
- Verifica que el Client ID sea de sandbox
- Asegúrate de que `VITE_PAYPAL_ENVIRONMENT=sandbox`

### Configuración de Vite

El archivo `vite.config.ts` incluye configuración CORS optimizada:

```typescript
cors: {
  origin: [
    'http://localhost:8080',
    'https://www.sandbox.paypal.com',
    'https://js.paypal.com'
  ],
  credentials: true
}
```

### Comandos Útiles

```bash
# Reiniciar con simulación habilitada
npm run dev

# Verificar variables de entorno
echo $VITE_PAYPAL_SIMULATE_IN_DEV

# Limpiar cache y reiniciar
npm run dev -- --force
```

### Recomendaciones

1. **Desarrollo inicial**: Usa simulación para desarrollo rápido
2. **Pruebas de integración**: Usa sandbox real
3. **Antes de producción**: Prueba con cuentas reales en sandbox
4. **Producción**: Cambia a Client ID live y environment='live'

### Notas Importantes

- La simulación NO procesa pagos reales
- Siempre prueba con sandbox antes de producción
- Los datos simulados son solo para desarrollo
- El modo simulación se indica claramente en la UI
