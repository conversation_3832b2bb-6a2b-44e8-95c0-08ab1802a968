// hooks/useBookingPersistence.ts
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';

// Tipo simple para datos persistidos (compatible con formData)
interface PersistedBookingData {
  from?: string;
  to?: string;
  date?: string | Date | null; // Flexible para manejar diferentes formatos
  time?: string;
  returnDate?: string | Date | null;
  returnTime?: string;
  adults?: string;
  kids?: string;
  passengers?: string;
  roundTrip?: boolean;
  zone_id?: number;
  
  // Hotel seleccionado
  selectedHotel?: {
    id: number;
    name: string;
    zone_id: number;
    zone_name?: string;
    address?: string;
  } | null;
  
  // Información adicional de contacto
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  
  // Información del servicio seleccionado
  selectedService?: {
    id: string;
    name: string;
    price: number;
    fleet?: any; // Temporal, se puede tipar mejor después
  };
  
  // Metadatos útiles
  createdAt?: string;
  lastModified?: string;
}

const STORAGE_KEY = 'bajatravel-booking-session';

// Function that only reads from storage without triggering state updates
const getStoredBookingData = (): PersistedBookingData | null => {
  try {
    const saved = sessionStorage.getItem(STORAGE_KEY);
    if (saved) {
      return JSON.parse(saved);
    }
    return null;
  } catch (error) {
    console.error('Error loading booking data:', error);
    return null;
  }
};

export const useBookingPersistence = () => {
  const navigate = useNavigate();
  const [bookingData, setBookingData] = useState<PersistedBookingData | null>(null);

  // Cargar datos al montar el componente
  useEffect(() => {
    const data = getStoredBookingData();
    if (data) {
      setBookingData(data);
    }
  }, []);

  const saveBookingData = (data: Partial<PersistedBookingData>) => {
    try {
      const currentData = getStoredBookingData() || {};
      
      // Manejar específicamente el cambio de hotel para actualizar zone_id
      const updatedData = { 
        ...currentData, 
        ...data,
        lastModified: new Date().toISOString()
      };

      // Si el hotel cambió, actualizar también el zone_id
      if (data.selectedHotel) {
        updatedData.zone_id = data.selectedHotel.zone_id;
      }
      
      // Si roundTrip cambió, asegurarnos de que se actualice
      if (data.roundTrip !== undefined) {
        updatedData.roundTrip = data.roundTrip;
      }
      
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify(updatedData));
      setBookingData(updatedData);
      
      // console.log('📦 Booking data saved with updates:', updatedData);
      return updatedData;
    } catch (error) {
      console.error('Error saving booking data:', error);
      return null;
    }
  };


  // Function that reads from storage AND updates state (for useEffect and manual loading)
  const loadBookingData = (): PersistedBookingData | null => {
    try {
      const data = getStoredBookingData();
      if (data) {
        setBookingData(data);
      }
      return data;
    } catch (error) {
      console.error('Error loading booking data:', error);
      return null;
    }
  };

  const clearBookingData = () => {
    try {
      sessionStorage.removeItem(STORAGE_KEY);
      setBookingData(null);
      console.log('🗑️ Booking data cleared');
    } catch (error) {
      console.error('Error clearing booking data:', error);
    }
  };

  const saveAndNavigate = (data: any, path: string = '/booking-service') => {
    const savedData = saveBookingData(data);
    
    // También pasar por navigation state para compatibilidad
    navigate(path, { state: savedData });
    
    console.log(`🚀 Navigating to ${path} with data:`, savedData);
  };

  const submitToBackend = async (contactData: Record<string, any>) => {
    const fullData = { ...bookingData, ...contactData };
    
    try {
      console.log('📤 Submitting to backend:', fullData);
      
      // Aquí harías la llamada a tu backend PHP
      const response = await fetch('/api/booking/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(fullData)
      });
      
      if (response.ok) {
        clearBookingData(); // Limpiar después del envío exitoso
        return await response.json();
      } else {
        throw new Error('Failed to submit booking');
      }
      
    } catch (error) {
      console.error('Error submitting booking:', error);
      throw error;
    }
  };

  return {
    bookingData,
    saveBookingData,
    loadBookingData,
    getStoredBookingData, // Export the non-state-updating version
    clearBookingData,
    saveAndNavigate,
    submitToBackend
  };
};

export { getStoredBookingData };
export default useBookingPersistence;