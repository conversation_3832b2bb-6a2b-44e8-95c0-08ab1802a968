# Environment Variables Setup

## Overview

Este proyecto utiliza variables de entorno para configurar diferentes aspectos de la aplicación. La configuración está organizada en archivos específicos para cada entorno.

## Archivos de Configuración

### 📁 `.env`
- **Propósito**: Configuración para desarrollo local
- **Uso**: Variables para el entorno de desarrollo
- **Incluido en**: `.gitignore` (no se sube al repositorio)

### 📁 `.env.production`
- **Propósito**: Configuración para producción
- **Uso**: Variables para el entorno de producción
- **Incluido en**: Repositorio (valores de ejemplo)

### 📁 `.env.example`
- **Propósito**: Plantilla con todas las variables disponibles
- **Uso**: Referencia para crear archivos de entorno
- **Incluido en**: Repositorio

## Configuración Inicial

### 1. Para Desarrollo Local

```bash
# Copia el archivo de ejemplo
cp .env.example .env

# Edita las variables según tu entorno local
# Especialmente importante configurar:
# - VITE_PAYPAL_CLIENT_ID (tu Client ID de PayPal Sandbox)
# - VITE_FACEBOOK_PIXEL_ID (opcional)
```

### 2. Para Producción

```bash
# Edita .env.production con los valores reales de producción
# Especialmente importante:
# - VITE_PAYPAL_CLIENT_ID (tu Client ID de PayPal Live)
# - VITE_PAYPAL_ENVIRONMENT=live
# - VITE_FACEBOOK_PIXEL_ID (tu Pixel ID real)
```

## Variables Importantes

### 🔧 API Configuration
```bash
VITE_API_BASE_URL_LOCAL=https://api-web-btt.transroute.com.mx
VITE_API_BASE_URL_REMOTE=https://api-web-btt.transroute.com.mx
VITE_API_TIMEOUT=10000
```

### 💳 PayPal Configuration
```bash
# Sandbox (Desarrollo)
VITE_PAYPAL_CLIENT_ID=tu_sandbox_client_id
VITE_PAYPAL_ENVIRONMENT=sandbox

# Live (Producción)
VITE_PAYPAL_CLIENT_ID=tu_live_client_id
VITE_PAYPAL_ENVIRONMENT=live
```

### 🎯 Feature Flags
```bash
VITE_ENABLE_BOOKING=true
VITE_ENABLE_PAYMENTS=true
VITE_ENABLE_ANALYTICS=true
```

### 📊 Facebook Pixel
```bash
VITE_FACEBOOK_PIXEL_ID=tu_pixel_id
```

## Obtener Credenciales

### PayPal
1. Ve a [PayPal Developer Dashboard](https://developer.paypal.com/)
2. Crea una nueva aplicación
3. Obtén el Client ID para Sandbox y Live
4. Configura las variables correspondientes

### Facebook Pixel
1. Ve a [Facebook Business Manager](https://business.facebook.com/)
2. Accede a Events Manager
3. Crea o selecciona tu Pixel
4. Copia el Pixel ID

## Seguridad

### ✅ Variables Seguras (Frontend)
- `VITE_PAYPAL_CLIENT_ID` - Seguro exponer en frontend
- `VITE_FACEBOOK_PIXEL_ID` - Seguro exponer en frontend
- Todas las variables `VITE_*` - Diseñadas para frontend

### ❌ Variables Sensibles (Backend Only)
- PayPal Client Secret - NUNCA en frontend
- API Keys privadas - NUNCA en frontend
- Tokens de autenticación - NUNCA en frontend

## Troubleshooting

### Problema: PayPal no carga
```bash
# Verifica que las variables estén configuradas
echo $VITE_PAYPAL_CLIENT_ID
echo $VITE_PAYPAL_ENVIRONMENT

# Revisa la consola del navegador para errores
```

### Problema: Variables no se cargan
```bash
# Asegúrate de que las variables empiecen con VITE_
# Reinicia el servidor de desarrollo después de cambios
npm run dev
```

### Problema: Diferencias entre entornos
```bash
# Verifica que uses el archivo correcto:
# Desarrollo: .env
# Producción: .env.production
```

## Comandos Útiles

```bash
# Verificar variables cargadas (desarrollo)
npm run dev -- --debug

# Construir para producción
npm run build

# Vista previa de producción local
npm run preview
```

## Notas Importantes

1. **Nunca subas archivos `.env` al repositorio**
2. **Usa diferentes Client IDs para sandbox y producción**
3. **Verifica que `VITE_ENABLE_PAYMENTS=true` para usar PayPal**
4. **Las variables deben empezar con `VITE_` para ser accesibles en el frontend**
5. **Reinicia el servidor después de cambiar variables de entorno**

## Estructura de Archivos

```
bajatravel-frontend/
├── .env                 # Desarrollo (no en git)
├── .env.production      # Producción (en git, sin secretos)
├── .env.example         # Plantilla (en git)
└── ENV_SETUP.md         # Esta documentación
```
